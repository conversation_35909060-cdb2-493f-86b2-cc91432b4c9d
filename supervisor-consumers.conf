[supervisord]
logfile=var/log/supervisord.log

[group:consumers]
programs=async_test_created,sync_rpc,async_rpc

[program:async_test_created]
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/queue.log
process_name=%(program_name)s_%(process_num)02d
command=bin/console messenger:consume async_test_created --memory-limit=128M --time-limit=3600

[program:sync_rpc]
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/queue.log
process_name=%(program_name)s_%(process_num)02d
command=bin/console messenger:consume sync_rpc --memory-limit=128M --time-limit=3600

[program:async_rpc]
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/queue.log
process_name=%(program_name)s_%(process_num)02d
command=bin/console messenger:consume async_rpc --memory-limit=128M --time-limit=3600
