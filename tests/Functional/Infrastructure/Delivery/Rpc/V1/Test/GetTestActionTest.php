<?php

namespace App\Tests\Functional\Infrastructure\Delivery\Rpc\V1\Test;


use App\Application\Query\Test\Get\GetTestQuery;
use App\Infrastructure\Rpc\RpcResultStatus;
use App\Tests\Shared\Factory\RpcCommandFactory;
use App\Tests\Functional\RpcFunctionalTestCase;
use App\Tests\Shared\Factory\TestFactory;

final class GetTestActionTest extends RpcFunctionalTestCase
{

    protected const COMMAND = RpcCommandFactory::GET_TEST_ACTION;

    protected function setUp(): void
    {
        parent::setUp();
    }


    public function testGetSingleTest() : void
    {

        $query = new GetTestQuery((string) TestFactory::TEST_ID);

        $response = $this->call([$query]);

        $this->assertSame(RpcResultStatus::SUCCESS, $response->getStatus());

        /** @var array<mixed,mixed> $result */
        $result = $response->getResult();

        $this->assertNotEmpty($result);
        $this->assertSame((int) $query->getTestId(), $result['id']);
    }

}
