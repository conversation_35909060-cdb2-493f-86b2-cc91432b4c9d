<?php

declare(strict_types=1);

namespace App\Tests\Functional\Infrastructure\Delivery\Api\V1\Test;

use App\Infrastructure\Rpc\RpcCommand;
use App\Tests\Functional\Infrastructure\Delivery\Api\V1\ApiV1TestCase;
use App\Tests\Shared\Factory\RpcResultFactory;

final class GetTestsActionTest extends ApiV1TestCase
{
    protected const string ROUTE = '/en/tests';
    protected const string METHOD = 'GET';

    protected function setUp(): void
    {
        parent::setUp();
    }

    public function testGetTestsReturns200(): void
    {
        $this->mockRpcResponse(
            function (RpcCommand $rpcCommand) {
                if ('tenants.getSingleTest' !== $rpcCommand->getCommand()) {
                    return false;
                }
                return true;
            },
            RpcResultFactory::getRpcCommandResult(result: null),
        );

        $this->makeRequest();

        $this->assertResponseSuccess(['tests' => '@array@', 'rpcTest' => null]);
    }
}
