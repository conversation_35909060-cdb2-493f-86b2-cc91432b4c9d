<?php

declare(strict_types=1);

namespace App\Tests\Functional\Infrastructure\Delivery\Api\V1\Test;

use App\Tests\Functional\Infrastructure\Delivery\Api\V1\ApiV1TestCase;

final class SendTestEmailActionTest extends ApiV1TestCase
{
    protected const string ROUTE = '/en/test-email';
    protected const string METHOD = 'POST';

    protected function setUp(): void
    {
        parent::setUp();
    }

    public function testSendTestEmailReturns200(): void
    {
        $this->makeRequest();

        $this->assertResponseStatusCodeSame(204);

        $this->assertEmailCount(1);
    }
}
