<?php

declare(strict_types=1);

namespace App\Tests\Functional\Infrastructure\Delivery\Api\V1;

use App\Tests\Functional\FunctionalTestCase;
use Symfony\Component\HttpFoundation\Response;

abstract class ApiV1TestCase extends FunctionalTestCase
{
    protected const string BASE_ROUTE = '/tenants/v1';
    protected const string LOCALE = 'en';

    protected function assertJsonResponse(Response $response): void
    {
        $this->assertTrue($response->isSuccessful());
        $this->assertJson($response->getContent());
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->setUserHeaders();
    }

    protected function setUserHeaders(): void
    {
        $this->client->setServerParameter('HTTP_ACCEPT', 'application/json');
        $this->client->setServerParameter('CONTENT_TYPE', 'application/json');
        $this->client->setServerParameter('HTTP_AUTHORIZATION', 'Bearer ' . $this->getTestToken());
    }

    protected function getTestToken(): string
    {
        return 'test-token';
    }
}
