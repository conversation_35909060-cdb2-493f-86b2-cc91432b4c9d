<?php

declare(strict_types=1);

namespace App\Tests\Functional\Infrastructure\Delivery\Api\V1;

use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Shared\Trait\RpcTestBootTrait;
use ReflectionException;
use Symfony\Component\HttpFoundation\Response;

final class JsonRpcActionTest extends FunctionalTestCase
{
    use RpcTestBootTrait;

    protected const string ROUTE = '/jsonrpc';
    protected const string METHOD = 'POST';
    protected const string SERVICE = 'tenants';

    protected function setUp(): void
    {
        parent::setUp();
    }

    /**
     * @dataProvider provideSuccessData
     *
     * @throws ReflectionException
     */
    public function testJsonRpcReturnSuccess(array $data, ?array $expectedResponse, int $statusCode = Response::HTTP_OK): void
    {
        $this->emulateRpcCommand(self::SERVICE, $data['method'], $data['params'] ?? []);
        $this->makeRequest(data: $data);
        $this->assertResponseStatusCodeSame($statusCode);

        if (null === $expectedResponse) {
            $this->assertNull($this->getDecodedJsonResponse());
        } else {
            $this->assertMatchesPattern($expectedResponse, $this->getDecodedJsonResponse());
        }
    }

    /**
     * @dataProvider provideErrorData
     *
     * @throws ReflectionException
     */
    public function testJsonRpcReturnError(array $data, array $expectedResponse): void
    {
        $this->emulateRpcCommand(self::SERVICE, $data['method'], $data['params'] ?? []);
        $this->makeRequest(data: $data);
        $this->assertResponseStatusCodeSame(200);
        $this->assertMatchesPattern($expectedResponse, $this->getDecodedJsonResponse());
    }

    public function testJsonRpcReturnBadRequest(): void
    {
        $this->makeRequest(data: ['jsonrpc' => '1.0', 'method' => 'getSingleTest']);
        $this->assertResponseErrors(['message' => 'Bad Request']);
    }


    public function provideSuccessData(): array
    {
        return [
            'testNotification' => [
                [
                    'jsonrpc' => '2.0',
                    'method' => 'notificationCommand',
                ],
                null,
                Response::HTTP_NO_CONTENT,
            ],
            'getSingleTest' => [
                [
                    'jsonrpc' => '2.0',
                    'method' => 'getSingleTest',
                    'params' => ['query' => ["testId" => "1"]],
                    'id' => 'cff92407-d772-447f-af6b-d53722361948',
                ],
                [
                    'jsonrpc' => '2.0',
                    'result' => [
                        'id' => 1,
                        'value' => 'test value 1',
                        'createdAt' => '@date@',

                    ],
                    'id' => 'cff92407-d772-447f-af6b-d53722361948',
                ],
                Response::HTTP_OK,
            ],
        ];
    }

    public function provideErrorData(): array
    {
        return [
            'commandNotFound' => [
                [
                    'jsonrpc' => '2.0',
                    'method' => 'invalidCommand',
                    'id' => 'cff92407-d772-447f-af6b-d53722361948',
                ],
                [
                    'jsonrpc' => '2.0',
                    'error' => [
                        'code' => -32601,
                        'message' => 'Command not found',
                    ],
                    'id' => 'cff92407-d772-447f-af6b-d53722361948',
                ],
            ]
        ];
    }
}
