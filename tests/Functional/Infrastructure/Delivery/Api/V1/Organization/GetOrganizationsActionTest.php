<?php

declare(strict_types=1);

namespace App\Tests\Functional\Infrastructure\Delivery\Api\V1\Organization;

use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Shared\Factory\OrganizationFactory;

final class GetOrganizationsActionTest extends FunctionalTestCase
{
    protected const string ROUTE = '/tenants/v1/{_locale}/organizations';
    protected const string METHOD = 'GET';
    protected const string LOCALE = 'en';

    public function testGetOrganizationsWorks(): void
    {
        $route = str_replace('{_locale}', self::LOCALE, self::ROUTE);
        $this->client->request(self::METHOD, $route);
        $this->assertResponseStatusCodeSame(200);

        $response = $this->getDecodedJsonResponse();
        $this->assertArrayHasKey('data', $response);
        $this->assertArrayHasKey('items', $response['data']);
        $this->assertArrayHasKey('metadata', $response['data']);
        $this->assertIsArray($response['data']['items']);

        // Check metadata structure
        $metadata = $response['data']['metadata'];
        $this->assertArrayHasKey('limit', $metadata);
        $this->assertArrayHasKey('offset', $metadata);
        $this->assertArrayHasKey('total', $metadata);

        // Should have at least the fixture organization
        $this->assertGreaterThanOrEqual(1, count($response['data']['items']));

        // Check that fixture organization is present
        $organizationNames = array_column($response['data']['items'], 'name');
        $this->assertContains(OrganizationFactory::NAME, $organizationNames);
    }

    public function testGetOrganizationsWithPagination(): void
    {
        $route = str_replace('{_locale}', self::LOCALE, self::ROUTE) . '?page=1&limit=5';
        $this->client->request(self::METHOD, $route);
        $this->assertResponseStatusCodeSame(200);

        $response = $this->getDecodedJsonResponse();
        $this->assertArrayHasKey('data', $response);
        $this->assertArrayHasKey('metadata', $response['data']);

        $metadata = $response['data']['metadata'];
        $this->assertEquals(0, $metadata['offset']); // page 1 means offset 0
        $this->assertEquals(5, $metadata['limit']);
        $this->assertIsInt($metadata['total']);

        // Should not return more than the limit
        $this->assertLessThanOrEqual(5, count($response['data']['items']));
    }

    public function testGetOrganizationsWithFilters(): void
    {
        // Test filtering by name
        $route = str_replace('{_locale}', self::LOCALE, self::ROUTE) . '?filters[name]=' . urlencode(OrganizationFactory::NAME);
        $this->client->request(self::METHOD, $route);
        $this->assertResponseStatusCodeSame(200);

        $response = $this->getDecodedJsonResponse();
        $this->assertArrayHasKey('data', $response);
        $this->assertArrayHasKey('items', $response['data']);

        // Should find the organization with the exact name
        $organizations = $response['data']['items'];
        $this->assertNotEmpty($organizations);

        // All returned organizations should match the filter
        foreach ($organizations as $org) {
            $this->assertStringContainsString(OrganizationFactory::NAME, $org['name']);
        }
    }

    public function testGetOrganizationsWithEmailFilter(): void
    {
        // Test filtering by email
        $route = str_replace('{_locale}', self::LOCALE, self::ROUTE) . '?filters[email]=' . urlencode(OrganizationFactory::EMAIL);
        $this->client->request(self::METHOD, $route);
        $this->assertResponseStatusCodeSame(200);

        $response = $this->getDecodedJsonResponse();
        $this->assertArrayHasKey('data', $response);
        $this->assertArrayHasKey('items', $response['data']);

        // Should find the organization with the exact email
        $organizations = $response['data']['items'];
        $this->assertNotEmpty($organizations);

        // All returned organizations should match the filter
        foreach ($organizations as $org) {
            $this->assertStringContainsString(OrganizationFactory::EMAIL, $org['email']);
        }
    }

    public function testGetOrganizationsEmptyResult(): void
    {
        // Test with a filter that should return no results
        $route = str_replace('{_locale}', self::LOCALE, self::ROUTE) . '?filters[name]=' . urlencode('NonExistentOrganization12345');
        $this->client->request(self::METHOD, $route);
        $this->assertResponseStatusCodeSame(200);

        $response = $this->getDecodedJsonResponse();
        $this->assertArrayHasKey('data', $response);
        $this->assertArrayHasKey('items', $response['data']);
        $this->assertEmpty($response['data']['items']);
        $this->assertEquals(0, $response['data']['metadata']['total']);
    }
}
