<?php

declare(strict_types=1);

namespace App\Tests\Functional\Infrastructure\Delivery\Api\V1\Organization;

use App\Tests\Functional\FunctionalTestCase;

use App\Tests\Shared\Factory\OrganizationFactory;
use App\Tests\Shared\Random\Generator;
use Exception;

final class CreateOrganizationActionTest extends FunctionalTestCase
{
    protected const string ROUTE = '/tenants/v1/{_locale}/organizations';
    protected const string METHOD = 'POST';
    protected const string LOCALE = 'en';

    /**
     * @dataProvider provideCreateOrganizationValidData
     */
    public function testCreateOrganizationWorks(array $data, array $response): void
    {
        $route = str_replace('{_locale}', self::LOCALE, self::ROUTE);
        $this->client->jsonRequest(method: self::METHOD, uri: $route, parameters: $data);
        $this->assertResponseStatusCodeSame(201);
        $this->assertMatchesPattern(['data' => $response], $this->getDecodedJsonResponse());
    }

    public function testCreateOrganizationFailsWithExistingName(): void
    {
        $data = [
            'name' => OrganizationFactory::NAME,
            'address' => OrganizationFactory::ADDRESS,
            'email' => OrganizationFactory::EMAIL,
            'phone' => OrganizationFactory::PHONE,
        ];
        $route = str_replace('{_locale}', self::LOCALE, self::ROUTE);
        $this->client->jsonRequest(self::METHOD, $route, $data);
        $this->assertResponseStatusCodeSame(422);

        $expectedResponse = [
            'errors' => [
                'name' => sprintf('Name %s already exists', OrganizationFactory::NAME),
            ],
        ];
        $this->assertMatchesPattern($expectedResponse, $this->getDecodedJsonResponse());
    }

    /**
     * @dataProvider provideCreateOrganizationInvalidData
     */
    public function testCreateOrganizationFailsWithInvalidData(array $data, array $errors): void
    {
        $route = str_replace('{_locale}', self::LOCALE, self::ROUTE);
        $this->client->jsonRequest(self::METHOD, $route, $data);

        $this->assertResponseStatusCodeSame(400);
        $expectedResponse = ['errors' => $errors];
        $this->assertMatchesPattern($expectedResponse, $this->getDecodedJsonResponse());
    }

    public function provideCreateOrganizationValidData(): array
    {
        return [
            'validData' => [
                [
                    'name' => OrganizationFactory::NON_EXISTING_NAME,
                    'email' => OrganizationFactory::EMAIL,
                    'phone' => OrganizationFactory::PHONE,
                    'address' => OrganizationFactory::ADDRESS,
                ],
                [
                    'id' => '@uuid@',
                    'name' => OrganizationFactory::NON_EXISTING_NAME,
                    'email' => OrganizationFactory::EMAIL,
                    'phone' => OrganizationFactory::PHONE,
                    'address' => OrganizationFactory::ADDRESS,
                    'deletedAt' => null,
                    'createdAt' => '@datetime@.isInDateFormat("Y-m-d\TH:i:sP")',
                    'updatedAt' => '@datetime@.isInDateFormat("Y-m-d\TH:i:sP")',
                ],
            ],
            'allAllowedCharactersMail' => [
                [
                    'name' => OrganizationFactory::NON_EXISTING_NAME,
                    'email' => OrganizationFactory::NON_EXISTING_EMAIL_ALL_ALLOWED_CHARACTERS,
                    'phone' => OrganizationFactory::PHONE,
                    'address' => OrganizationFactory::ADDRESS,
                ],
                [
                    'id' => '@uuid@',
                    'name' => OrganizationFactory::NON_EXISTING_NAME,
                    'email' => OrganizationFactory::NON_EXISTING_EMAIL_ALL_ALLOWED_CHARACTERS,
                    'phone' => OrganizationFactory::PHONE,
                    'address' => OrganizationFactory::ADDRESS,
                    'deletedAt' => null,
                    'createdAt' => '@datetime@.isInDateFormat("Y-m-d\TH:i:sP")',
                    'updatedAt' => '@datetime@.isInDateFormat("Y-m-d\TH:i:sP")',
                ],
            ],
            'nullablePhoneNumber' => [
                [
                    'name' => OrganizationFactory::NON_EXISTING_NAME,
                    'email' => OrganizationFactory::EMAIL,
                    'phone' => null,
                    'address' => OrganizationFactory::ADDRESS,
                ],
                [
                    'id' => '@uuid@',
                    'name' => OrganizationFactory::NON_EXISTING_NAME,
                    'email' => OrganizationFactory::EMAIL,
                    'phone' => null,
                    'address' => OrganizationFactory::ADDRESS,
                    'deletedAt' => null,
                    'createdAt' => '@datetime@.isInDateFormat("Y-m-d\TH:i:sP")',
                    'updatedAt' => '@datetime@.isInDateFormat("Y-m-d\TH:i:sP")',
                ],
            ],
        ];
    }

    /**
     * @throws Exception
     */
    public function provideCreateOrganizationInvalidData(): array
    {
        return [
            'emptyBody' => [
                [],
                [
                    'name' => 'This value should not be blank.',
                    'email' => 'This value should not be blank.',
                    'address' => 'This value should not be blank.',
                ],
            ],
            'emptyName' => [
                ['name' => '', 'address' => 'sample address', 'email' => '<EMAIL>', 'phone' => '************'],
                ['name' => 'This value should not be blank.'],
            ],
            'emptyEmail' => [
                ['name' => 'Valid Name', 'email' => '', 'address' => 'sample address', 'phone' => '(*************'],
                ['email' => 'This value should not be blank.'],
            ],
            'invalidEmail' => [
                ['name' => 'Valid Name', 'email' => 'invalidEmail', 'address' => 'sample address', 'phone' => '+************'],
                ['email' => 'This value is not a valid email address.'],
            ],

            'longerName' => [
                [
                    'name' => Generator::string(101),
                    'email' => '<EMAIL>',
                    'phone' => '**************',
                    'address' => 'sample address',
                ],
                ['name' => 'This value is too long. It should have 100 characters or less.'],
            ],
            'shortPhone' => [
                [
                    'name' => 'Valid Name',
                    'email' => '<EMAIL>',
                    'phone' => '12',
                    'address' => 'sample address',
                ],
                ['phone' => 'This value is too short. It should have 3 characters or more.'],
            ],
            'longerPhone' => [
                [
                    'name' => 'Valid Name',
                    'email' => '<EMAIL>',
                    'phone' => '(+359) 123 456 789 123 456 789 123 456 789 123 456 789',
                    'address' => 'sample address',
                ],
                ['phone' => 'This value is too long. It should have 30 characters or less.'],
            ],
            'invalidPhone' => [
                [
                    'name' => 'Valid Name',
                    'email' => '<EMAIL>',
                    'phone' => '0883_5402a010',
                    'address' => 'sample address',
                ],
                ['phone' => 'Invalid phone number.'],
            ],
        ];
    }
}
