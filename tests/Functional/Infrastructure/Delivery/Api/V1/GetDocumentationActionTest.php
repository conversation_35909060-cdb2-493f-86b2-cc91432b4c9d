<?php

declare(strict_types=1);

namespace App\Tests\Functional\Infrastructure\Delivery\Api\V1;

use App\Infrastructure\Delivery\Api\V1\GetDocumentationAction;

final class GetDocumentationActionTest extends ApiV1TestCase
{
    protected const string ROUTE = '/tenants/v1/docs';
    protected const string METHOD = 'GET';
    protected const array HTTP_METHODS = ['GET', 'POST', 'PUT', 'DELETE'];

    protected const string WRONG_ROUTE = '/tenants/v1/wrong-route-for-testing';


    public function testGetDocsReturnsOK(): void
    {
        $this->client->request(self::METHOD, self::ROUTE);

        $this->assertResponseStatusCodeSame(200);
        $expectedResponse = [
            'openapi' => '3.0.0',
            'info' => [
                'title' => 'Obrazcov Tenants API',
                'description' => 'Obrazcov Tenants API',
                'version' => '0.1',
            ],
            'paths' => [
                '/tenants/v1/{_locale}/tests' => [
                    'get' => '@array@',
                ],
                '/tenants/v1/{_locale}/test-email' => [
                    'post' => '@array@',
                ],
                '/tenants/v1/jsonrpc' => [
                    'post' => '@array@',
                ],
                '/tenants/v1/{_locale}/organizations' => [
                    'get' => '@array@',
                    'post' => '@array@',
                ],
                '/tenants/v1/{_locale}/organizations/{id}' => [
                    'get' => '@array@',
                    'put' => '@array@',
                    'delete' => '@array@',
                ],
            ],
            'components' => '@array@',
            'tags' => '@array@'
        ];

        $this->assertMatchesPattern($expectedResponse, $this->getDecodedJsonResponse());
    }

    public function testRouteWithInvalidHttpMethodReturnsNotFound(): void
    {
        foreach (self::HTTP_METHODS as $method) {
            if (self::METHOD === $method) {
                continue;
            }

            $this->client->request($method, self::ROUTE);

            $this->assertResponseNotFound();
        }
    }

    public function testWrongRouteReturnsNotFound(): void
    {
        $this->client->request('GET', self::WRONG_ROUTE);

        $this->assertResponseNotFound();
    }

    public function testUnknownExceptionWillBeHandled(): void
    {
        $container = self::getContainer();

        /** @phpstan-ignore method.unresolvableReturnType */
        $mock = $this->createMock(GetDocumentationAction::class);

        $mock->expects($this->once())
            ->method('__invoke')
            ->will($this->throwException(new \Exception()));
        $container->set(GetDocumentationAction::class, $mock);

        $this->client->request(self::METHOD, self::ROUTE);

        $this->assertResponseErrors(['message' => 'Internal Server Error'], 500);
    }
}
