<?php

declare(strict_types=1);

namespace App\Tests\Functional;

use App\Infrastructure\Rpc\RpcResult;
use App\Infrastructure\Rpc\Transport\RpcResultReceiverInterface;
use App\Tests\Double\Rpc\Transport\MockResultReceiver;
use Closure;
use Coduo\PHPMatcher\PHPUnit\PHPMatcherAssertions;
use RuntimeException;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\DomCrawler\Crawler;
use Symfony\Component\HttpFoundation\File\UploadedFile;

abstract class FunctionalTestCase extends WebTestCase
{
    use PHPMatcherAssertions;

    protected const string BASE_ROUTE = '/tenants/v1';
    protected const string ROUTE = '/docs';
    protected const string METHOD = 'GET';

    protected KernelBrowser $client;

    protected function setUp(): void
    {
        parent::setUp();

        $this->client = static::createClient();
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        static::ensureKernelShutdown();
    }

    /**
     * @param array<mixed,mixed> $pathParams
     * @param array<mixed,mixed> $queryParams
     * @param array<mixed,mixed> $data
     * @param array<mixed,mixed> $headers
     */
    protected function makeRequest(
        array $pathParams = [],
        array $queryParams = [],
        array $data = [],
        array $headers = [],
    ): Crawler {
        return $this->client->jsonRequest(
            method: static::METHOD,
            uri: $this->buildUrl($pathParams, $queryParams),
            parameters: $data,
            server: array_merge($headers)
        );
    }

    /**
     * @param array<mixed,mixed> $pathParams
     * @param array<mixed,mixed> $queryParams
     * @param array<mixed,mixed> $data
     * @param array<mixed,mixed> $headers
     */
    protected function makeFileRequest(
        UploadedFile $file,
        array $pathParams = [],
        array $queryParams = [],
        array $data = [],
        array $headers = [],
    ): Crawler {
        return $this->client->request(
            method: 'POST',
            uri: $this->buildUrl($pathParams, $queryParams),
            parameters: $data,
            files: ['file' => $file],
            server: array_merge($headers)
        );
    }

    /**
     * @param array<mixed,mixed> $pathParams
     * @param array<mixed,mixed> $queryParams
     */
    protected function buildUrl(array $pathParams = [], array $queryParams = []): string
    {
        $pathKeys = [];
        $pathValues = [];
        foreach ($pathParams as $key => $value) {
            $pathKeys[] = '{' . $key . '}';
            $pathValues[] = $value;
        }
        $uri = str_replace($pathKeys, $pathValues, self::BASE_ROUTE . static::ROUTE);

        if ([] === $queryParams) {
            return $uri;
        }

        return $uri . '?' . http_build_query($queryParams);
    }

    /**
     * @return null|array<mixed,mixed>
     */
    protected function getDecodedJsonResponse(): ?array
    {
        /** @var string $responseContent */
        $responseContent = $this->client->getResponse()->getContent();
        /** @var array<mixed,mixed> $response */
        $response = json_decode($responseContent, true);

        return $response;
    }

    /**
     * @param null|array<mixed,mixed> $expectedData
     */
    protected function assertResponseSuccess(?array $expectedData, int $statusCode = 200): void
    {
        $this->assertResponseStatusCodeSame($statusCode);
        $this->assertMatchesPattern($expectedData, $this->getDecodedJsonResponse());
    }

    protected function assertResponseNotFound(string $message = 'Not Found'): void
    {
        $this->assertResponseStatusCodeSame(404);
        $this->assertMatchesPattern(['message' => $message], $this->getDecodedJsonResponse());
    }

    /**
     * @param array<mixed,mixed> $expectedErrors
     */
    protected function assertResponseErrors(array $expectedErrors, int $statusCode = 400): void
    {
        $this->assertResponseStatusCodeSame($statusCode);
        $this->assertMatchesPattern(['errors' => $expectedErrors], $this->getDecodedJsonResponse());
    }

    protected function mockRpcResponse(Closure $matchCallback, RpcResult $response): void
    {

        $rpcResultReceiver = self::getContainer()->get(RpcResultReceiverInterface::class);

        if (!$rpcResultReceiver instanceof MockResultReceiver) {
            throw new RuntimeException('RpcResultReceiver must be an instance of MockResultReceiver');
        }

        $rpcResultReceiver->mock($matchCallback, $response);
    }

}
