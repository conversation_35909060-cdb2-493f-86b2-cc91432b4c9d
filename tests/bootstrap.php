<?php

use Symfony\Component\Dotenv\Dotenv;

require dirname(__DIR__).'/vendor/autoload.php';

(new Dotenv())->bootEnv(dirname(__DIR__).'/.env');

echo "Creating test database if it doesn't exist...\n";
passthru('bin/console doctrine:database:create --env=test --if-not-exists');

echo "Dropping test database schema...\n";
passthru('bin/console doctrine:schema:drop --env=test --force');
passthru('bin/console doctrine:migrations:version --env=test --delete --all --no-interaction');

echo "Running migrations for test database...\n";
passthru('bin/console doctrine:migrations:migrate --env=test --no-interaction');

echo "Loading fixtures...\n";
passthru('bin/console doctrine:fixtures:load --env=test --no-interaction');
