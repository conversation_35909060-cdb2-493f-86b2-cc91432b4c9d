<?php

declare(strict_types=1);

namespace App\Tests\Unit\Domain\Model\Test;

use App\Domain\Model\Test\Test;
use App\Tests\Shared\Factory\TestFactory;
use App\Tests\Unit\UnitTestCase;
use DateTimeImmutable;

class TestTest extends UnitTestCase
{
    private Test $test;
    private DateTimeImmutable $createdAt;
    private DateTimeImmutable $updatedAt;

    protected function setUp(): void
    {
        parent::setUp();

        $this->createdAt = new DateTimeImmutable('2025-04-18 12:00:00');
        $this->updatedAt = new DateTimeImmutable('2025-04-18 13:00:00');

        $this->test = new Test(TestFactory::TEST_VALUE);
        $this->test->setCreatedAt($this->createdAt);
        $this->test->setUpdatedAt($this->updatedAt);
    }

    public function testConstructor(): void
    {
        $test = new Test(TestFactory::TEST_VALUE);
        $this->assertEquals(TestFactory::TEST_VALUE, $test->getValue());
        $this->assertNull($test->getId());
        $this->assertNull($test->getCreatedAt());
        $this->assertNull($test->getUpdatedAt());
    }

    public function testGetValue(): void
    {
        $this->assertEquals(TestFactory::TEST_VALUE, $this->test->getValue());
    }

    public function testSetValue(): void
    {
        $newValue = TestFactory::NON_EXISTING_TEST_VALUE;
        $this->test->setValue($newValue);
        $this->assertEquals($newValue, $this->test->getValue());
    }

    public function testGetSetId(): void
    {
        $this->assertNull($this->test->getId());
        $this->test->setId(TestFactory::TEST_ID);
        $this->assertEquals(TestFactory::TEST_ID, $this->test->getId());
    }

    public function testGetSetCreatedAt(): void
    {
        $this->assertEquals($this->createdAt, $this->test->getCreatedAt());

        $newDate = new DateTimeImmutable('2025-04-18 14:00:00');
        $this->test->setCreatedAt($newDate);
        $this->assertEquals($newDate, $this->test->getCreatedAt());
    }

    public function testGetSetUpdatedAt(): void
    {
        $this->assertEquals($this->updatedAt, $this->test->getUpdatedAt());

        $newDate = new DateTimeImmutable('2025-04-18 15:00:00');
        $this->test->setUpdatedAt($newDate);
        $this->assertEquals($newDate, $this->test->getUpdatedAt());
    }
}
