<?php

declare(strict_types=1);

namespace App\Tests\Unit\Domain\Model\Organization;

use App\Domain\Model\Organization\Organization;
use App\Tests\Unit\UnitTestCase;
use ReflectionClass;

class OrganizationTest extends UnitTestCase
{
    private const ID = '550e8400-e29b-41d4-a716-446655440000';
    private const NAME = 'Test Organization';
    private const EMAIL = '<EMAIL>';
    private const PHONE = '+1234567890';
    private const ADDRESS = '123 Test St, Test City';

    private Organization $organization;

    public function testItCanBeCreated(): void
    {
        $this->assertSame(self::NAME, $this->organization->getName());
        $this->assertSame(self::EMAIL, $this->organization->getEmail());
        $this->assertSame(self::PHONE, $this->organization->getPhone());
        $this->assertSame(self::ADDRESS, $this->organization->getAddress());
        $this->assertNull($this->organization->getDeletedAt());
        $this->assertNotNull($this->organization->getCreatedAt());
    }

    public function testItCanBeUpdated(): void
    {
        $newName = 'Updated Organization';
        $newEmail = '<EMAIL>';
        $newPhone = '+0987654321';
        $newAddress = '456 Updated St, Test City';

        $this->organization->update($newName, $newEmail, $newPhone, $newAddress);

        $this->assertSame($newName, $this->organization->getName());
        $this->assertSame($newEmail, $this->organization->getEmail());
        $this->assertSame($newPhone, $this->organization->getPhone());
        $this->assertSame($newAddress, $this->organization->getAddress());
    }

    public function testItCanSetIndividualProperties(): void
    {
        $newName = 'Updated Organization';
        $newEmail = '<EMAIL>';
        $newPhone = '+0987654321';
        $newAddress = '456 Updated St, Test City';

        $this->organization->setName($newName);
        $this->organization->setEmail($newEmail);
        $this->organization->setPhone($newPhone);
        $this->organization->setAddress($newAddress);

        $this->assertSame($newName, $this->organization->getName());
        $this->assertSame($newEmail, $this->organization->getEmail());
        $this->assertSame($newPhone, $this->organization->getPhone());
        $this->assertSame($newAddress, $this->organization->getAddress());

        // Verify updatedAt is set
        $this->assertNotNull($this->organization->getUpdatedAt());
    }

    public function testItCanBeSoftDeleted(): void
    {
        $this->assertNull($this->organization->getDeletedAt());

        $this->organization->markAsDeleted();

        $this->assertNotNull($this->organization->getDeletedAt());
    }

    public function testItCanBeRestored(): void
    {
        $this->organization->markAsDeleted();
        $this->assertNotNull($this->organization->getDeletedAt());

        $this->organization->restore();

        $this->assertNull($this->organization->getDeletedAt());
    }

    public function testItReturnsId(): void
    {
        $this->assertSame(self::ID, $this->organization->getId());
    }

    protected function setUp(): void
    {
        $this->organization = new Organization(
            self::NAME,
            self::EMAIL,
            self::PHONE,
            self::ADDRESS
        );

        $reflection = new ReflectionClass($this->organization);
        $idProperty = $reflection->getProperty('id');
        $idProperty->setAccessible(true);
        $idProperty->setValue($this->organization, self::ID);
    }
}
