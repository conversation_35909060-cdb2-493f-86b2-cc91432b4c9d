<?php

declare(strict_types=1);

namespace App\Tests\Unit\Domain\Model\Company;

use App\Domain\Model\Company\Company;
use App\Domain\Model\Organization\Organization;
use App\Tests\Unit\UnitTestCase;
use ReflectionClass;

class CompanyTest extends UnitTestCase
{
    private const ID = '550e8400-e29b-41d4-a716-446655440000';
    private const NAME = 'Test Property Company';
    private const EMAIL = '<EMAIL>';
    private const PHONE = '+1234567890';
    private const ADDRESS = '123 Property St, Test City';
    private const DEFAULT_LANGUAGE = 'en';
    private const DEFAULT_CURRENCY = 'USD';

    private Company $company;
    private Organization $organization;

    public function testItCanBeCreated(): void
    {
        $this->assertSame($this->organization, $this->company->getOrganization());
        $this->assertSame(self::NAME, $this->company->getName());
        $this->assertSame(self::EMAIL, $this->company->getEmail());
        $this->assertSame(self::PHONE, $this->company->getPhone());
        $this->assertSame(self::ADDRESS, $this->company->getAddress());
        $this->assertSame(self::DEFAULT_LANGUAGE, $this->company->getDefaultLanguage());
        $this->assertSame(self::DEFAULT_CURRENCY, $this->company->getDefaultCurrency());
        $this->assertNull($this->company->getDeletedAt());
        $this->assertNotNull($this->company->getCreatedAt());
    }

    public function testItCanBeUpdated(): void
    {
        $newName = 'Updated Property Company';
        $newEmail = '<EMAIL>';
        $newPhone = '+0987654321';
        $newAddress = '456 Updated St, Test City';
        $newLanguage = 'bg';
        $newCurrency = 'EUR';

        $this->company->update(
            $newName,
            $newEmail,
            $newPhone,
            $newAddress,
            $newLanguage,
            $newCurrency
        );

        $this->assertSame($newName, $this->company->getName());
        $this->assertSame($newEmail, $this->company->getEmail());
        $this->assertSame($newPhone, $this->company->getPhone());
        $this->assertSame($newAddress, $this->company->getAddress());
        $this->assertSame($newLanguage, $this->company->getDefaultLanguage());
        $this->assertSame($newCurrency, $this->company->getDefaultCurrency());
    }

    public function testItCanSetIndividualProperties(): void
    {
        $newName = 'Updated Property Company';
        $newEmail = '<EMAIL>';
        $newPhone = '+0987654321';
        $newAddress = '456 Updated St, Test City';
        $newLanguage = 'bg';
        $newCurrency = 'EUR';

        $this->company->setName($newName);
        $this->company->setEmail($newEmail);
        $this->company->setPhone($newPhone);
        $this->company->setAddress($newAddress);
        $this->company->setDefaultLanguage($newLanguage);
        $this->company->setDefaultCurrency($newCurrency);

        $this->assertSame($newName, $this->company->getName());
        $this->assertSame($newEmail, $this->company->getEmail());
        $this->assertSame($newPhone, $this->company->getPhone());
        $this->assertSame($newAddress, $this->company->getAddress());
        $this->assertSame($newLanguage, $this->company->getDefaultLanguage());
        $this->assertSame($newCurrency, $this->company->getDefaultCurrency());

        // Verify updatedAt is set
        $this->assertNotNull($this->company->getUpdatedAt());
    }

    public function testItCanBeSoftDeleted(): void
    {
        $this->assertNull($this->company->getDeletedAt());

        $this->company->markAsDeleted();

        $this->assertNotNull($this->company->getDeletedAt());
    }

    public function testItCanBeRestored(): void
    {
        $this->company->markAsDeleted();
        $this->assertNotNull($this->company->getDeletedAt());

        $this->company->restore();

        $this->assertNull($this->company->getDeletedAt());
    }

    public function testItReturnsId(): void
    {
        $this->assertSame(self::ID, $this->company->getId());
    }

    protected function setUp(): void
    {
        $this->organization = new Organization(
            'Test Organization',
            '<EMAIL>',
            '+1234567890',
            '123 Org St, Test City'
        );

        $this->company = new Company(
            $this->organization,
            self::NAME,
            self::EMAIL,
            self::PHONE,
            self::ADDRESS,
            self::DEFAULT_LANGUAGE,
            self::DEFAULT_CURRENCY
        );

        $reflection = new ReflectionClass($this->company);
        $idProperty = $reflection->getProperty('id');
        $idProperty->setAccessible(true);
        $idProperty->setValue($this->company, self::ID);
    }
}
