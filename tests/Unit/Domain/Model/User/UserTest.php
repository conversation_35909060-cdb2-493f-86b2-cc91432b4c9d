<?php

declare(strict_types = 1);

namespace App\Tests\Unit\Domain\Model\User;

use App\Domain\Model\User\User;
use App\Tests\Shared\Factory\UserFactory;
use App\Tests\Unit\UnitTestCase;
use DateTimeImmutable;

final class UserTest extends UnitTestCase
{
    public function testConstructor(): void
    {
        $user = new User(UserFactory::EMAIL, UserFactory::PASSWORD, UserFactory::IS_VERIFIED);
        $user->setId(1);

        $this->assertSame(1, $user->getId());
        $this->assertSame(UserFactory::EMAIL, $user->getEmail());
        $this->assertSame(UserFactory::PASSWORD, $user->getPassword());
        $this->assertTrue($user->isVerified());
        $this->assertInstanceOf(\DateTimeImmutable::class, $user->getCreatedAt());
        $this->assertInstanceOf(\DateTimeImmutable::class, $user->getUpdatedAt());
    }

    /**
     * @dataProvider provideGetters
     */
    public function testGetters(string $getter, mixed $expectedValue): void
    {
        $user = new User(UserFactory::EMAIL, UserFactory::PASSWORD, UserFactory::IS_VERIFIED);
        $user->setId(1);

        $actualValue = $user->{$getter}();

        if ($expectedValue instanceof \DateTimeImmutable) {
            $this->assertInstanceOf(\DateTimeImmutable::class, $actualValue);
        } else {
            $this->assertEquals($expectedValue, $actualValue);
        }
    }

    /**
     * @dataProvider provideSetters
     */
    public function testSetters(string $setter, mixed $value, string $getter): void
    {
        $user = new User(UserFactory::EMAIL, UserFactory::PASSWORD, UserFactory::IS_VERIFIED);

        $user->{$setter}($value);

        if ($value instanceof \DateTimeImmutable) {
            $this->assertInstanceOf(\DateTimeImmutable::class, $user->{$getter}());
        } else {
            $this->assertEquals($value, $user->{$getter}());
        }
    }

    /**
     * @return array<int,list<bool|DateTimeImmutable|int|string>>
     */
    public function provideGetters(): array
    {
        return [
            ['getId', 1],
            ['getEmail', UserFactory::EMAIL],
            ['getPassword', UserFactory::PASSWORD],
            ['isVerified', true],
            ['getCreatedAt', new \DateTimeImmutable()],
            ['getUpdatedAt', new \DateTimeImmutable()],
        ];
    }

    /**
     * @return array<int,list<bool|DateTimeImmutable|int|string>>
     */
    public function provideSetters(): array
    {
        return [
            ['setId', 2, 'getId'],
            ['setEmail', '<EMAIL>', 'getEmail'],
            ['setPassword', 'new_password_123', 'getPassword'],
            ['setIsVerified', false, 'isVerified'],
            ['setCreatedAt', new \DateTimeImmutable('2025-04-18 12:00:00'), 'getCreatedAt'],
            ['setUpdatedAt', new \DateTimeImmutable('2025-04-18 13:00:00'), 'getUpdatedAt'],
        ];
    }
}
