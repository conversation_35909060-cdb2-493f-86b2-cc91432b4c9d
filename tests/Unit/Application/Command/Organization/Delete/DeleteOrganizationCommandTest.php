<?php

declare(strict_types=1);

namespace App\Tests\Unit\Application\Command\Organization\Delete;

use App\Application\Command\Organization\Delete\DeleteOrganizationCommand;
use App\Tests\Unit\UnitTestCase;

class DeleteOrganizationCommandTest extends UnitTestCase
{
    private const ID = '550e8400-e29b-41d4-a716-446655440000';

    public function testConstructorAndGetters(): void
    {
        $command = new DeleteOrganizationCommand(self::ID);

        $this->assertSame(self::ID, $command->getId());
    }
}
