<?php

declare(strict_types=1);

namespace App\Tests\Unit\Application\Command\Organization\Create;

use App\Application\Command\Organization\Create\CreateOrganizationCommand;
use App\Tests\Shared\Factory\OrganizationFactory;
use App\Tests\Unit\UnitTestCase;

class CreateOrganizationCommandTest extends UnitTestCase
{
    public function testConstructorAndGetters(): void
    {
        $command = new CreateOrganizationCommand(
            OrganizationFactory::NAME,
            OrganizationFactory::EMAIL,
            OrganizationFactory::PHONE,
            OrganizationFactory::ADDRESS
        );

        $this->assertSame(OrganizationFactory::NAME, $command->getName());
        $this->assertSame(OrganizationFactory::EMAIL, $command->getEmail());
        $this->assertSame(OrganizationFactory::PHONE, $command->getPhone());
        $this->assertSame(OrganizationFactory::ADDRESS, $command->getAddress());
    }

    public function testConstructorWithNullPhone(): void
    {
        $command = new CreateOrganizationCommand(
            OrganizationFactory::NAME,
            OrganizationFactory::EMAIL,
            null,
            OrganizationFactory::ADDRESS
        );

        $this->assertSame(OrganizationFactory::NAME, $command->getName());
        $this->assertSame(OrganizationFactory::EMAIL, $command->getEmail());
        $this->assertNull($command->getPhone());
        $this->assertSame(OrganizationFactory::ADDRESS, $command->getAddress());
    }
}
