<?php

declare(strict_types=1);

namespace App\Tests\Unit\Application\Command\Organization\Update;

use App\Application\Command\Organization\Update\UpdateOrganizationCommand;
use App\Tests\Shared\Factory\OrganizationFactory;
use App\Tests\Unit\UnitTestCase;

class UpdateOrganizationCommandTest extends UnitTestCase
{
    private const ID = '550e8400-e29b-41d4-a716-446655440000';

    public function testConstructorAndGetters(): void
    {
        $command = new UpdateOrganizationCommand(
            self::ID,
            OrganizationFactory::NAME,
            OrganizationFactory::EMAIL,
            OrganizationFactory::PHONE,
            OrganizationFactory::ADDRESS
        );

        $this->assertSame(self::ID, $command->getId());
        $this->assertSame(OrganizationFactory::NAME, $command->getName());
        $this->assertSame(OrganizationFactory::EMAIL, $command->getEmail());
        $this->assertSame(OrganizationFactory::PHONE, $command->getPhone());
        $this->assertSame(OrganizationFactory::ADDRESS, $command->getAddress());
    }

    public function testConstructorWithNullPhone(): void
    {
        $command = new UpdateOrganizationCommand(
            self::ID,
            OrganizationFactory::NAME,
            OrganizationFactory::EMAIL,
            null,
            OrganizationFactory::ADDRESS
        );

        $this->assertSame(self::ID, $command->getId());
        $this->assertSame(OrganizationFactory::NAME, $command->getName());
        $this->assertSame(OrganizationFactory::EMAIL, $command->getEmail());
        $this->assertNull($command->getPhone());
        $this->assertSame(OrganizationFactory::ADDRESS, $command->getAddress());
    }
}
