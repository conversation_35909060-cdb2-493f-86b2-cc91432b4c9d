<?php
declare(strict_types=1);

namespace App\Tests\Unit\Application\Command\Test\Create;

use App\Application\Command\Test\Create\CreateTestCommand;
use App\Tests\Unit\UnitTestCase;

class CreateTestCommandTest extends UnitTestCase
{
    public function testGetValue(): void
    {
        $value = 'test-value';
        $command = new CreateTestCommand($value);

        $this->assertEquals($value, $command->getValue());
    }
}
