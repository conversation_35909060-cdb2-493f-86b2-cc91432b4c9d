<?php
declare(strict_types=1);

namespace App\Tests\Unit\Application\Command\Test\SendMail;

use App\Application\Command\Test\SendMail\SendTestMailCommand;
use App\Tests\Unit\UnitTestCase;

class SendTestMailCommandTest extends UnitTestCase
{
    public function testCommandCanBeCreated(): void
    {
        $command = new SendTestMailCommand();

        $this->assertInstanceOf(SendTestMailCommand::class, $command);
    }
}
