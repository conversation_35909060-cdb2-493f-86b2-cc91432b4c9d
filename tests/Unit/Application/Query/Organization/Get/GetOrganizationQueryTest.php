<?php

declare(strict_types=1);

namespace App\Tests\Unit\Application\Query\Organization\Get;

use App\Application\Query\Organization\Get\GetOrganizationQuery;
use App\Tests\Unit\UnitTestCase;

class GetOrganizationQueryTest extends UnitTestCase
{
    private const ID = '550e8400-e29b-41d4-a716-446655440000';

    public function testConstructorAndGetters(): void
    {
        $query = new GetOrganizationQuery(self::ID);

        $this->assertSame(self::ID, $query->getId());
    }
}
