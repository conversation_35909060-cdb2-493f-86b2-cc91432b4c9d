<?php

declare(strict_types=1);

namespace App\Tests\Unit\Application\Query\Organization\GetAll;

use App\Application\Query\Organization\GetAll\GetAllOrganizationsQuery;
use App\Tests\Unit\UnitTestCase;

class GetAllOrganizationsQueryTest extends UnitTestCase
{
    private const LIMIT = 10;
    private const OFFSET = 0;
    private const FILTERS = ['name' => ['eq' => 'Test Organization']];
    private const SORT = 'name:asc';

    public function testConstructorAndGetters(): void
    {
        $query = new GetAllOrganizationsQuery(
            self::LIMIT,
            self::OFFSET,
            self::FILTERS,
            self::SORT
        );

        $this->assertSame(self::LIMIT, $query->getLimit());
        $this->assertSame(self::OFFSET, $query->getOffset());
        $this->assertSame(self::FILTERS, $query->getFilters());
        $this->assertSame(self::SORT, $query->getSort());
    }

    public function testConstructorWithEmptyFilters(): void
    {
        $query = new GetAllOrganizationsQuery(
            self::LIMIT,
            self::OFFSET,
            [],
            self::SORT
        );

        $this->assertSame(self::LIMIT, $query->getLimit());
        $this->assertSame(self::OFFSET, $query->getOffset());
        $this->assertSame([], $query->getFilters());
        $this->assertSame(self::SORT, $query->getSort());
    }
}
