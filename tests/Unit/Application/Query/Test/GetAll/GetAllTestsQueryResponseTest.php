<?php

declare(strict_types=1);

namespace App\Tests\Unit\Application\Query\Test\GetAll;

use App\Application\Query\Test\Get\GetTestQueryResponse;
use App\Application\Query\Test\GetAll\GetAllTestsQueryResponse;
use App\Tests\Shared\Factory\TestFactory;
use App\Tests\Unit\UnitTestCase;
use DateTimeImmutable;

class GetAllTestsQueryResponseTest extends UnitTestCase
{
    private GetAllTestsQueryResponse $response;

    protected function setUp(): void
    {
        $testResponse = new GetTestQueryResponse(
            TestFactory::TEST_ID,
            TestFactory::TEST_VALUE,
            DateTimeImmutable::createFromFormat('Y-m-d H:i:s', TestFactory::TEST_DATE)
        );

        $this->response = new GetAllTestsQueryResponse(
            [$testResponse],
            $testResponse
        );
    }

    public function testConstructor(): void
    {
        $this->assertNotEmpty($this->response->getTests());
        $this->assertInstanceOf(GetTestQueryResponse::class, $this->response->getRpcTest());
    }

    public function testGetTests(): void
    {
        $tests = $this->response->getTests();
        $this->assertNotEmpty($tests);
        $this->assertInstanceOf(GetTestQueryResponse::class, $tests[0]);
    }

    public function testGetRpcTest(): void
    {
        $rpcTest = $this->response->getRpcTest();
        $this->assertNotNull($rpcTest);
        $this->assertInstanceOf(GetTestQueryResponse::class, $rpcTest);
    }

    public function testWithNullRpcTest(): void
    {
        $response = new GetAllTestsQueryResponse([], null);

        $this->assertEmpty($response->getTests());
        $this->assertNull($response->getRpcTest());
    }
}
