<?php

declare(strict_types=1);

namespace App\Tests\Unit\Application\Query\Test\Get;

use App\Application\Query\Test\Get\GetTestQueryResponse;
use App\Tests\Shared\Factory\TestFactory;
use App\Tests\Unit\UnitTestCase;
use DateTimeImmutable;

class GetTestQueryResponseTest extends UnitTestCase
{
    private GetTestQueryResponse $response;

    protected function setUp(): void
    {
        $this->response = new GetTestQueryResponse(
            TestFactory::TEST_ID,
            TestFactory::TEST_VALUE,
            DateTimeImmutable::createFromFormat('Y-m-d H:i:s', TestFactory::TEST_DATE)
        );
    }

    public function testConstructor(): void
    {
        $this->assertEquals(TestFactory::TEST_ID, $this->response->getId());
        $this->assertEquals(TestFactory::TEST_VALUE, $this->response->getValue());
        $this->assertEquals(TestFactory::TEST_DATE, $this->response->getCreatedAt());
    }

    public function testFromArray(): void
    {
        $data = [
            'id' => TestFactory::TEST_ID,
            'value' => TestFactory::TEST_VALUE,
            'createdAt' => TestFactory::TEST_DATE,
        ];

        $response = GetTestQueryResponse::fromArray($data);

        $this->assertEquals(TestFactory::TEST_ID, $response->getId());
        $this->assertEquals(TestFactory::TEST_VALUE, $response->getValue());
        $this->assertEquals(TestFactory::TEST_DATE, $response->getCreatedAt());
    }

    public function testGetters(): void
    {
        $this->assertEquals(TestFactory::TEST_ID, $this->response->getId());
        $this->assertEquals(TestFactory::TEST_VALUE, $this->response->getValue());
        $this->assertEquals(TestFactory::TEST_DATE, $this->response->getCreatedAt());
    }
}
