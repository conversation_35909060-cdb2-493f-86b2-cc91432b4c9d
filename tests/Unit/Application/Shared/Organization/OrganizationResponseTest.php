<?php

declare(strict_types=1);

namespace App\Tests\Unit\Application\Shared\Organization;

use App\Application\Shared\Organization\OrganizationResponse;
use App\Domain\Model\Organization\Organization;
use App\Tests\Shared\Factory\OrganizationFactory;
use App\Tests\Unit\UnitTestCase;
use DateTimeImmutable;

class OrganizationResponseTest extends UnitTestCase
{
    private const ID = '550e8400-e29b-41d4-a716-446655440000';
    private DateTimeImmutable $createdAt;
    private DateTimeImmutable $updatedAt;

    protected function setUp(): void
    {
        parent::setUp();

        $this->createdAt = new DateTimeImmutable('2025-01-01T00:00:00+00:00');
        $this->updatedAt = new DateTimeImmutable('2025-01-01T12:00:00+00:00');
    }

    public function testConstructorAndGetters(): void
    {
        $response = new OrganizationResponse(
            self::ID,
            OrganizationFactory::NAME,
            OrganizationFactory::EMAIL,
            OrganizationFactory::PHONE,
            OrganizationFactory::ADDRESS,
            null,
            $this->createdAt,
            $this->updatedAt
        );

        $this->assertSame(self::ID, $response->getId());
        $this->assertSame(OrganizationFactory::NAME, $response->getName());
        $this->assertSame(OrganizationFactory::EMAIL, $response->getEmail());
        $this->assertSame(OrganizationFactory::PHONE, $response->getPhone());
        $this->assertSame(OrganizationFactory::ADDRESS, $response->getAddress());
        $this->assertNull($response->getDeletedAt());
        $this->assertSame('2025-01-01T00:00:00+00:00', $response->getCreatedAt());
        $this->assertSame('2025-01-01T12:00:00+00:00', $response->getUpdatedAt());
    }

    public function testConstructorWithNullPhone(): void
    {
        $response = new OrganizationResponse(
            self::ID,
            OrganizationFactory::NAME,
            OrganizationFactory::EMAIL,
            null,
            OrganizationFactory::ADDRESS,
            null,
            $this->createdAt,
            $this->updatedAt
        );

        $this->assertSame(self::ID, $response->getId());
        $this->assertSame(OrganizationFactory::NAME, $response->getName());
        $this->assertSame(OrganizationFactory::EMAIL, $response->getEmail());
        $this->assertNull($response->getPhone());
        $this->assertSame(OrganizationFactory::ADDRESS, $response->getAddress());
        $this->assertNull($response->getDeletedAt());
        $this->assertSame('2025-01-01T00:00:00+00:00', $response->getCreatedAt());
        $this->assertSame('2025-01-01T12:00:00+00:00', $response->getUpdatedAt());
    }

    public function testConstructorWithDeletedAt(): void
    {
        $deletedAt = new DateTimeImmutable('2025-01-02T00:00:00+00:00');

        $response = new OrganizationResponse(
            self::ID,
            OrganizationFactory::NAME,
            OrganizationFactory::EMAIL,
            OrganizationFactory::PHONE,
            OrganizationFactory::ADDRESS,
            $deletedAt,
            $this->createdAt,
            $this->updatedAt
        );

        $this->assertSame(self::ID, $response->getId());
        $this->assertSame(OrganizationFactory::NAME, $response->getName());
        $this->assertSame(OrganizationFactory::EMAIL, $response->getEmail());
        $this->assertSame(OrganizationFactory::PHONE, $response->getPhone());
        $this->assertSame(OrganizationFactory::ADDRESS, $response->getAddress());
        $this->assertSame('2025-01-02T00:00:00+00:00', $response->getDeletedAt());
        $this->assertSame('2025-01-01T00:00:00+00:00', $response->getCreatedAt());
        $this->assertSame('2025-01-01T12:00:00+00:00', $response->getUpdatedAt());
    }

    public function testFromOrganization(): void
    {
        $organization = OrganizationFactory::create([
            'id' => self::ID,
            'name' => OrganizationFactory::NAME,
            'email' => OrganizationFactory::EMAIL,
            'phone' => OrganizationFactory::PHONE,
            'address' => OrganizationFactory::ADDRESS
        ]);

        $response = OrganizationResponse::fromOrganization($organization);

        $this->assertSame(self::ID, $response->getId());
        $this->assertSame(OrganizationFactory::NAME, $response->getName());
        $this->assertSame(OrganizationFactory::EMAIL, $response->getEmail());
        $this->assertSame(OrganizationFactory::PHONE, $response->getPhone());
        $this->assertSame(OrganizationFactory::ADDRESS, $response->getAddress());
        $this->assertNull($response->getDeletedAt());
        $this->assertNotNull($response->getCreatedAt());
        $this->assertNull($response->getUpdatedAt());
    }

    public function testFromOrganizationWithNullPhone(): void
    {
        $organization = new Organization(
            OrganizationFactory::NAME,
            OrganizationFactory::EMAIL,
            null,
            OrganizationFactory::ADDRESS,
            self::ID
        );

        $response = OrganizationResponse::fromOrganization($organization);

        $this->assertSame(self::ID, $response->getId());
        $this->assertSame(OrganizationFactory::NAME, $response->getName());
        $this->assertSame(OrganizationFactory::EMAIL, $response->getEmail());
        $this->assertNull($response->getPhone());
        $this->assertSame(OrganizationFactory::ADDRESS, $response->getAddress());
        $this->assertNull($response->getDeletedAt());
        $this->assertNotNull($response->getCreatedAt());
        $this->assertNull($response->getUpdatedAt());
    }
}
