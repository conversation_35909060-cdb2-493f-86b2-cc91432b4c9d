<?php

declare(strict_types=1);

namespace App\Tests\Unit\Infrastructure\Http;

use App\Infrastructure\Http\RequestMapper;
use App\Tests\Unit\Infrastructure\Http\RequestMappingType\ArrayType;
use App\Tests\Unit\Infrastructure\Http\RequestMappingType\BoolType;
use App\Tests\Unit\Infrastructure\Http\RequestMappingType\DateTimeType;
use App\Tests\Unit\Infrastructure\Http\RequestMappingType\DefaultValueType;
use App\Tests\Unit\Infrastructure\Http\RequestMappingType\FloatType;
use App\Tests\Unit\Infrastructure\Http\RequestMappingType\IntType;
use App\Tests\Unit\Infrastructure\Http\RequestMappingType\NullableArrayType;
use App\Tests\Unit\Infrastructure\Http\RequestMappingType\NullableBoolType;
use App\Tests\Unit\Infrastructure\Http\RequestMappingType\NullableDateTimeType;
use App\Tests\Unit\Infrastructure\Http\RequestMappingType\NullableFloatType;
use App\Tests\Unit\Infrastructure\Http\RequestMappingType\NullableIntType;
use App\Tests\Unit\Infrastructure\Http\RequestMappingType\NullableStringType;
use App\Tests\Unit\Infrastructure\Http\RequestMappingType\StringType;
use App\Tests\Unit\Infrastructure\Http\RequestMappingType\UnsupportedType;
use App\Tests\Unit\UnitTestCase;
use DateTimeImmutable;
use Exception;
use Symfony\Component\HttpFoundation\Request;

final class RequestMapperTest extends UnitTestCase
{
    /**
     * @param class-string $type
     * @dataProvider provideFromBodyWithNullableFieldsData
     */
    public function testFromBodyWithNullableFields(string $type, string $getterName): void
    {
        $request = new Request();
        $mapper = new RequestMapper();

        $typeInstance = $mapper->fromBody($request, $type);
        $this->assertInstanceOf($type, $typeInstance);
        $this->assertNull($typeInstance->$getterName());
    }

    /**
     * @param class-string $type
     * @dataProvider provideFromBodyWithNotNullableFieldsData
     */
    public function testFromBodyWithNotNullableFields(
        string $type,
        string $getterName,
        mixed $expectedEmptyValue
    ): void {
        $request = new Request();
        $mapper = new RequestMapper();
        $typeInstance = $mapper->fromBody($request, $type);
        $this->assertInstanceOf($type, $typeInstance);
        $realValue = $typeInstance->$getterName();
        if ($realValue instanceof DateTimeImmutable) {
            $this->assertLessThanOrEqual($expectedEmptyValue->getTimestamp() + 100, $realValue->getTimestamp());
        } else {
            $this->assertSame($expectedEmptyValue, $realValue);
        }
    }

    /**
     * @param class-string $type
     * @dataProvider provideFromBodyWithSetData
     * @param array<string, mixed> $content
     */
    public function testFromBodyWithSetData(
        string $type,
        string $getterName,
        array $content,
        mixed $expectedValue,
    ): void {
        /** @var non-empty-string $jsonContent */
        $jsonContent = json_encode($content);
        $request = new Request(content: $jsonContent);
        // This is need because RequestSubscriber can't be used here
        $request->request->replace(json_decode((string) $request->getContent(), true));
        $mapper = new RequestMapper();

        $typeInstance = $mapper->fromBody($request, $type);
        $this->assertInstanceOf($type, $typeInstance);

        $realValue = $typeInstance->$getterName();
        if ($realValue instanceof DateTimeImmutable) {
            // For invalid datetime strings, we just verify it's a recent timestamp
            if ($content['dateTime'] === 'test') {
                $this->assertGreaterThanOrEqual(time() - 10, $realValue->getTimestamp());
            } else {
                $this->assertEqualsWithDelta($expectedValue->getTimestamp(), $realValue->getTimestamp(), 2);
            }
        } else {
            $this->assertSame($expectedValue, $realValue);
        }
    }

    public function testFromBodyThrowsException(): void
    {
        /** @var non-empty-string $content */
        $content = json_encode(['unsupportedType' => 'test']);
        $request = new Request(content: $content);

        $request->request->replace(json_decode((string) $request->getContent(), true));
        $mapper = new RequestMapper();

        $this->expectException(Exception::class);
        $mapper->fromBody($request, UnsupportedType::class);
    }

    /**
     * @return array<string, array<mixed>>
     */
    public function provideFromBodyWithNullableFieldsData(): array
    {
        return [
            'nullableStringType' => [NullableStringType::class, 'getNullableString'],
            'nullableIntType' => [NullableIntType::class, 'getNullableInt'],
            'nullableFloatType' => [NullableFloatType::class, 'getNullableFloat'],
            'nullableBoolType' => [NullableBoolType::class, 'getNullableBool'],
            'nullableArrayType' => [NullableArrayType::class, 'getNullableArray'],
            'nullableDateTimeType' => [NullableDateTimeType::class, 'getNullableDateTime'],
        ];
    }

    /**
     * @return array<string, array<mixed>>
     */
    public function provideFromBodyWithNotNullableFieldsData(): array
    {
        return [
            'string' => [StringType::class, 'getString', ''],
            'int' => [IntType::class, 'getInt', 0],
            'float' => [FloatType::class, 'getFloat', 0.0],
            'bool' => [BoolType::class, 'getBool', false],
            'dateTime' => [DateTimeType::class, 'getDateTime', new DateTimeImmutable()],
            'array' => [ArrayType::class, 'getArray', []],
            'defaultValue' => [DefaultValueType::class, 'getDefault', DefaultValueType::DEFAULT_VALUE],
        ];
    }

    /**
     * @return array<string, array<mixed>>
     */
    public function provideFromBodyWithSetData(): array
    {
        return [
            'string' => [StringType::class, 'getString', ['string' => 'test'], 'test'],
            'intFromString' => [IntType::class, 'getInt', ['int' => '123'], 123],
            'int' => [IntType::class, 'getInt', ['int' => 123], 123],
            'float' => [FloatType::class, 'getFloat', ['float' => '12.32'], 12.32],
            'boolFrom1String' => [BoolType::class, 'getBool', ['bool' => '1'], true],
            'boolFrom0String' => [BoolType::class, 'getBool', ['bool' => '0'], false],
            'boolFromEmptyString' => [BoolType::class, 'getBool', ['bool' => ''], false],
            'boolFromTrue' => [BoolType::class, 'getBool', ['bool' => true], true],
            'boolFromFalse' => [BoolType::class, 'getBool', ['bool' => false], false],
            'dateTimeFromString' => [
                DateTimeType::class,
                'getDateTime',
                ['dateTime' => '2022-04-01 12:34:56'],
                new DateTimeImmutable('2022-04-01 12:34:56'),
            ],
            'dateTimeFromInvalidString' => [
                DateTimeType::class,
                'getDateTime',
                ['dateTime' => 'test'],
                new DateTimeImmutable('now')
            ],
            'dateTimeFromUnixTimestamp' => [
                DateTimeType::class,
                'getDateTime',
                ['dateTime' => 1650383951],
                new DateTimeImmutable('@' . 1650383951),
            ],
            'dateTimeFromInvalidUnixTimestamp' => [
                DateTimeType::class,
                'getDateTime',
                ['dateTime' => 9999999999],
                new DateTimeImmutable('@' . 9999999999),
            ],
            'array' => [
                ArrayType::class,
                'getArray',
                ['array' => [123, 12.34, 'test', false]],
                [123, 12.34, 'test', false],
            ],
            'defaultValue' => [
                DefaultValueType::class,
                'getDefault',
                ['default' => DefaultValueType::NON_DEFAULT_VALUE],
                DefaultValueType::NON_DEFAULT_VALUE,
            ],
        ];
    }

    public function testFromAttributes(): void
    {
        $request = new Request();
        $request->attributes->set('string', 'test');
        $request->attributes->set('int', '123');
        $request->attributes->set('float', '12.32');
        $request->attributes->set('bool', '1');

        $mapper = new RequestMapper();
        $typeInstance = $mapper->fromAttributes($request, StringType::class);

        $this->assertInstanceOf(StringType::class, $typeInstance);
        $this->assertSame('test', $typeInstance->getString());
    }

    public function testFromBodyAndAttributes(): void
    {
        $request = new Request();
        $request->attributes->set('string', 'test');
        $request->request->set('int', '123');

        $mapper = new RequestMapper();
        $typeInstance = $mapper->fromBodyAndAttributes($request, StringType::class);

        $this->assertInstanceOf(StringType::class, $typeInstance);
        $this->assertSame('test', $typeInstance->getString());
    }

    public function testFromHeaders(): void
    {
        $request = new Request();
        $request->headers->set('test-header', 'test-value');
        $fieldsMapping = ['test_header' => 'testHeader'];

        $mapper = new RequestMapper();
        $typeInstance = $mapper->fromHeaders($request, StringType::class, $fieldsMapping);

        $this->assertInstanceOf(StringType::class, $typeInstance);
        $this->assertSame('', $typeInstance->getString());
    }

    public function testFromAttributesAndHeaders(): void
    {
        $request = new Request();
        $request->attributes->set('string', 'test');
        $request->headers->set('test-header', 'test-value');
        $fieldsMapping = ['test_header' => 'testHeader'];

        $mapper = new RequestMapper();
        $typeInstance = $mapper->fromAttributesAndHeaders($request, StringType::class, $fieldsMapping);

        $this->assertInstanceOf(StringType::class, $typeInstance);
        $this->assertSame('test', $typeInstance->getString());
    }

       /**
     * @param class-string $type
     *
     * @dataProvider provideFromRequestWithSetData
     */
    public function testFromRequestAttributesWithSetData(
        string $type,
        string $getterName,
        array $attributes,
        mixed $expectedValue,
    ): void {
        $request = new Request(attributes: $attributes);
        $mapper = new RequestMapper();

        $typeInstance = $mapper->fromRequest($request, $type);
        $this->assertInstanceOf($type, $typeInstance);
        $this->assertEquals($expectedValue, $typeInstance->$getterName());
    }

        /**
     * @param class-string $type
     * @dataProvider provideFromQueryWithSetData
     */
    public function testFromQueryWithSetData(
        string $type,
        string $getterName,
        array $query,
        mixed $expectedValue,
    ): void {
        $request = new Request($query);
        $mapper = new RequestMapper();

        $typeInstance = $mapper->fromQuery($request, $type);
        $this->assertInstanceOf($type, $typeInstance);
        if ($expectedValue instanceof DateTimeImmutable) {
            $this->assertEquals($expectedValue, $typeInstance->$getterName());
        } else {
            $this->assertSame($expectedValue, $typeInstance->$getterName());
        }
    }

    // public function testMapHeaders(): void
    // {
    //     $headers = ['test-header' => ['test-value']];
    //     $fieldsMapping = ['test_header' => 'testHeader'];

    //     $mapper = new RequestMapper();
    //     $mappedHeaders = $mapper->mapHeaders($headers, $fieldsMapping);

    //     $this->assertArrayHasKey('testHeader', $mappedHeaders);
    //     $this->assertSame('test-value', $mappedHeaders['testHeader']);
    // }

    /**
     * @return array<string, array<mixed>>
     */
    public function provideFromQueryWithSetData(): array
    {
        return [
            'string' => [StringType::class, 'getString', ['string' => 'test'], 'test'],
            'int' => [IntType::class, 'getInt', ['int' => '123'], 123],
            'float' => [FloatType::class, 'getFloat', ['float' => '12.32'], 12.32],
            'boolFrom1String' => [BoolType::class, 'getBool', ['bool' => '1'], true],
            'boolFrom0String' => [BoolType::class, 'getBool', ['bool' => '0'], false],
            'boolFromEmptyString' => [BoolType::class, 'getBool', ['bool' => ''], false],
            'boolFromFalse' => [BoolType::class, 'getBool', ['bool' => 'false'], true],
            'dateTimeFromString' => [
                DateTimeType::class,
                'getDateTime',
                ['dateTime' => '2022-04-01 12:34:56'],
                new DateTimeImmutable('2022-04-01 12:34:56'),
            ],
            'array' => [
                ArrayType::class,
                'getArray',
                ['array' => [123, 12.34, 'test', false]],
                [123, 12.34, 'test', false],
            ],
            'defaultValue' => [
                DefaultValueType::class,
                'getDefault',
                ['default' => DefaultValueType::NON_DEFAULT_VALUE],
                DefaultValueType::NON_DEFAULT_VALUE,
            ],
        ];
    }

    /**
     * @return array<string, array<mixed>>
     */
    public function provideFromBodyAndAttributesWithSetData(): array
    {
        return [
            'string' => [StringType::class, 'getString', ['string' => 'test'], 'test'],
            'intFromString' => [IntType::class, 'getInt', ['int' => '123'], 123],
            'int' => [IntType::class, 'getInt', ['int' => 123], 123],
            'float' => [FloatType::class, 'getFloat', ['float' => '12.32'], 12.32],
            'boolFrom1String' => [BoolType::class, 'getBool', ['bool' => '1'], true],
            'boolFrom0String' => [BoolType::class, 'getBool', ['bool' => '0'], false],
            'boolFromEmptyString' => [BoolType::class, 'getBool', ['bool' => ''], false],
            'boolFromTrue' => [BoolType::class, 'getBool', ['bool' => true], true],
            'boolFromFalse' => [BoolType::class, 'getBool', ['bool' => false], false],
            'dateTimeFromString' => [
                DateTimeType::class,
                'getDateTime',
                ['dateTime' => '2022-04-01 12:34:56'],
                new DateTimeImmutable('2022-04-01 12:34:56'),
            ],
            'dateTimeFromUnixTimestamp' => [
                DateTimeType::class,
                'getDateTime',
                ['dateTime' => 1650383951],
                new DateTimeImmutable('@' . 1650383951),
            ],
            'dateTimeFromInvalidUnixTimestamp' => [
                DateTimeType::class,
                'getDateTime',
                ['dateTime' => 9999999999],
                new DateTimeImmutable('@' . 9999999999),
            ],
            'array' => [
                ArrayType::class,
                'getArray',
                ['array' => [123, 12.34, 'test', false]],
                [123, 12.34, 'test', false],
            ],
            'defaultValue' => [
                DefaultValueType::class,
                'getDefault',
                ['default' => DefaultValueType::NON_DEFAULT_VALUE],
                DefaultValueType::NON_DEFAULT_VALUE,
            ],
        ];
    }

    /**
     * @return array<string, array<mixed>>
     */
    public function provideFromRequestWithSetData(): array
    {
        return [
            'string' => [StringType::class, 'getString', ['string' => 'test'], 'test'],
            'intFromString' => [IntType::class, 'getInt', ['int' => '123'], 123],
            'int' => [IntType::class, 'getInt', ['int' => 123], 123],
            'float' => [FloatType::class, 'getFloat', ['float' => '12.32'], 12.32],
            'boolFrom1String' => [BoolType::class, 'getBool', ['bool' => '1'], true],
            'boolFrom0String' => [BoolType::class, 'getBool', ['bool' => '0'], false],
            'boolFromEmptyString' => [BoolType::class, 'getBool', ['bool' => ''], false],
            'boolFromTrue' => [BoolType::class, 'getBool', ['bool' => true], true],
            'boolFromFalse' => [BoolType::class, 'getBool', ['bool' => false], false],
            'dateTimeFromString' => [
                DateTimeType::class,
                'getDateTime',
                ['dateTime' => '2022-04-01 12:34:56'],
                new DateTimeImmutable('2022-04-01 12:34:56'),
            ],
            'dateTimeFromUnixTimestamp' => [
                DateTimeType::class,
                'getDateTime',
                ['dateTime' => 1650383951],
                new DateTimeImmutable('@' . 1650383951),
            ],
            'dateTimeFromInvalidUnixTimestamp' => [
                DateTimeType::class,
                'getDateTime',
                ['dateTime' => 9999999999],
                new DateTimeImmutable('@' . 9999999999),
            ],
            'array' => [
                ArrayType::class,
                'getArray',
                ['array' => [123, 12.34, 'test', false]],
                [123, 12.34, 'test', false],
            ],
            'defaultValue' => [
                DefaultValueType::class,
                'getDefault',
                ['default' => DefaultValueType::NON_DEFAULT_VALUE],
                DefaultValueType::NON_DEFAULT_VALUE,
            ],
        ];
    }

    /**
     * @return array<string, array<mixed>>
     */
    public function provideFromHeadersWithNullableFieldsData(): array
    {
        return [
            'nullableString' => [NullableStringType::class, 'getNullableString', ['nullableString' => 'nullable-string']],
            'nullableInt' => [NullableIntType::class, 'getNullableInt', ['nullableInt' => 'nullable-int']],
            'nullableFloat' => [NullableFloatType::class, 'getNullableFloat', ['nullableFloat' => 'nullable-float']],
            'nullableBool' => [NullableBoolType::class, 'getNullableBool', ['nullableBool' => 'nullable-bool']],
            'nullableDateTime' => [NullableDateTimeType::class, 'getNullableDateTime', ['nullableDateTime' => 'nullable-date-time']],
            'nullableArray' => [NullableArrayType::class, 'getNullableArray', ['nullableArray' => 'nullable-array']],
        ];
    }

    /**
     * @return array<string, array<mixed>>
     */
    public function provideFromHeadersWithNotNullableFieldsData(): array
    {
        return [
            'string' => [StringType::class, 'getString', '', ['string' => 'test-string']],
            'int' => [IntType::class, 'getInt', 0, ['int' => 'test-int']],
            'float' => [FloatType::class, 'getFloat', 0.0, ['float' => 'test-float']],
            'bool' => [BoolType::class, 'getBool', false, ['bool' => 'test-bool']],
            'dateTime' => [DateTimeType::class, 'getDateTime', new DateTimeImmutable(), ['dateTime' => 'test-date-time']],
            'array' => [ArrayType::class, 'getArray', [], ['array' => 'test-array']],
            'defaultValue' => [DefaultValueType::class, 'getDefault', [], ['default' => DefaultValueType::DEFAULT_VALUE]],
        ];
    }

    /**
     * @return array<string, array<mixed>>
     */
    public function provideFromHeadersWithSetData(): array
    {
        return [
            'string' => [StringType::class, 'getString', ['HTTP_test-string' => 'test'], 'test', ['string' => 'test-string']],
            'int' => [IntType::class, 'getInt', ['HTTP_test-int' => '123'], 123, ['int' => 'test-int']],
            'float' => [FloatType::class, 'getFloat', ['HTTP_test-float' => '12.32'], 12.32, ['float' => 'test-float']],
            'boolFrom1String' => [BoolType::class, 'getBool', ['HTTP_test-bool' => '1'], true, ['bool' => 'test-bool']],
            'boolFrom0String' => [BoolType::class, 'getBool', ['HTTP_test-bool' => '0'], false, ['bool' => 'test-bool']],
            'boolFromEmptyString' => [BoolType::class, 'getBool', ['HTTP_test-bool' => ''], false, ['bool' => 'test-bool']],
            'boolFromFalse' => [BoolType::class, 'getBool', ['HTTP_test-bool' => 'false'], true, ['bool' => 'test-bool']],
            'dateTimeFromString' => [
                DateTimeType::class,
                'getDateTime',
                ['HTTP_test-date-time' => '2022-04-01 12:34:56'],
                new DateTimeImmutable('2022-04-01 12:34:56'),
                ['dateTime' => 'test-date-time'],
            ],
            'dateTimeFromUnixTimestamp' => [
                DateTimeType::class,
                'getDateTime',
                ['HTTP_test-date-time' => '1650383951'],
                new DateTimeImmutable('@' . 1650383951),
                ['dateTime' => 'test-date-time'],
            ],
            'defaultValue' => [
                DefaultValueType::class,
                'getDefault',
                ['HTTP_test-default' => DefaultValueType::NON_DEFAULT_VALUE],
                DefaultValueType::NON_DEFAULT_VALUE,
                ['default' => 'test-default'],
            ],
        ];
    }
}
