<?php

declare(strict_types=1);

namespace App\Tests\Unit\Infrastructure\Http\RequestMappingType;

final class NullableArrayType
{

    /**
     * @param array<mixed, mixed> $nullableArray
     */
    public function __construct(private ?array $nullableArray)
    {
    }

    /**
     * @return array<mixed, mixed> $nullableArray
     */
    public function getNullableArray(): ?array
    {
        return $this->nullableArray;
    }
}
