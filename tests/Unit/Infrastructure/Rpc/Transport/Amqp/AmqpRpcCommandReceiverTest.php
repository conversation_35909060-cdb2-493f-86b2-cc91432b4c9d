<?php

declare(strict_types=1);

namespace App\Tests\Unit\Infrastructure\Rpc\Transport\Amqp;

use App\Domain\Model\Tenant\DbConfig;
use App\Domain\Model\Tenant\TenantServiceInterface;
use App\Domain\Model\Tenant\TenantStorageInterface;
use App\Infrastructure\Persistence\Connection\DoctrineTenantConnection;
use App\Infrastructure\Persistence\Connection\RedisTenantConnection;
use App\Infrastructure\Rpc\Exception\TimeoutException;
use App\Infrastructure\Rpc\Server\RpcCommandServerInterface;
use App\Infrastructure\Rpc\Transport\Amqp\AmqpRpcCommandReceiver;
use App\Tests\Shared\Factory\RpcCommandFactory;
use App\Tests\Shared\Factory\TenantFactory;
use App\Tests\Unit\UnitTestCase;
use PHPUnit\Framework\MockObject\MockObject;

final class AmqpRpcCommandReceiverTest extends UnitTestCase
{
    private RpcCommandServerInterface&MockObject $commandServer;
    private AmqpRpcCommandReceiver $commandReceiver;

    protected function setUp(): void
    {
        $this->commandServer = $this->createMock(RpcCommandServerInterface::class);

        $this->commandReceiver = new AmqpRpcCommandReceiver(
            $this->commandServer
        );
    }

    public function testInvoke(): void
    {
        $rpcCommand = RpcCommandFactory::getRpcCommand();

        $this->commandServer->expects($this->once())
            ->method('handle')
            ->with($rpcCommand);

        $this->commandReceiver->__invoke($rpcCommand);
    }

    public function testInvokeWithTenantIdInCommand(): void
    {
        $rpcCommand = RpcCommandFactory::getRpcCommand();

        $this->commandServer->expects($this->once())
            ->method('handle')
            ->with($rpcCommand);

        $this->commandReceiver->__invoke($rpcCommand);
    }

    public function testInvokeTimeout(): void
    {
        $rpcCommand = RpcCommandFactory::getRpcCommand();

        $this->commandServer->expects($this->once())
            ->method('handle')
            ->willThrowException(new TimeoutException());

        $this->commandReceiver->__invoke($rpcCommand);
    }
}
