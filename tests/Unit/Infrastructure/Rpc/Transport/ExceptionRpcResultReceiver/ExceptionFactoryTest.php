<?php

declare(strict_types=1);

namespace App\Tests\Unit\Infrastructure\Rpc\Transport\ExceptionRpcResultReceiver;

use App\Infrastructure\Rpc\Exception\CommandNotFoundException;
use App\Infrastructure\Rpc\Exception\InternalServerErrorException;
use App\Infrastructure\Rpc\Exception\InvalidParametersException;
use App\Infrastructure\Rpc\Exception\InvalidRequestException;
use App\Infrastructure\Rpc\RpcResult;
use App\Infrastructure\Rpc\RpcResultStatus;
use App\Infrastructure\Rpc\Transport\ExceptionRpcResultReceiver\ExceptionFactory;
use App\Tests\Shared\Factory\RpcResultFactory;
use App\Tests\Unit\UnitTestCase;

final class ExceptionFactoryTest extends UnitTestCase
{
    private ExceptionFactory $exceptionFactory;

    protected function setUp(): void
    {
        $this->exceptionFactory = new ExceptionFactory();
    }

    /**
     * @dataProvider rpcResultDataProvider
     *
     * @param class-string|null $expectedExceptionClass
     */
    public function testFromResult(
        RpcResult $rpcResult,
        ?string $expectedExceptionClass,
        ?string $expectedMessage = null,
        mixed $expectedExceptionData = null,
    ): void {
        $exception = $this->exceptionFactory->fromResult($rpcResult);

        if (null !== $expectedExceptionClass) {
            $this->assertInstanceOf($expectedExceptionClass, $exception);
            $this->assertEquals($expectedMessage, $exception->getMessage());

            if (method_exists($exception, 'getData') && InvalidRequestException::class == $expectedExceptionClass) {
                /* @var InvalidRequestException $exception */
                $this->assertSame($expectedExceptionData, $exception->getData());
            }
        } else {
            $this->assertNull($exception);
            $this->assertNull($expectedExceptionData);
        }
    }

    public function rpcResultDataProvider(): array
    {
        return [
            'success' => [
                'rpcResult' => RpcResultFactory::getRpcCommandResult(status: RpcResultStatus::SUCCESS),
                'expectedExceptionClass' => null,
                'expectedExceptionData' => null,
            ],
            'CommandNotFoundException' => [
                'rpcResult' => RpcResultFactory::getRpcCommandResult(
                    status: RpcResultStatus::ERROR,
                    result: [
                        'code' => -32601,
                        'message' => 'Not found',
                    ]
                ),
                'expectedExceptionClass' => CommandNotFoundException::class,
                'expectedExceptionMessage' => 'Not found',
                'expectedExceptionData' => null,
            ],
            'InvalidParametersException' => [
                'rpcResult' => RpcResultFactory::getRpcCommandResult(
                    status: RpcResultStatus::ERROR,
                    result: [
                        'code' => -32602,
                        'message' => 'Invalid parameters',
                    ]
                ),
                'expectedExceptionClass' => InvalidParametersException::class,
                'expectedExceptionMessage' => 'Invalid parameters',
                'expectedExceptionData' => null,
            ],
            'InternalServerErrorException' => [
                'rpcResult' => RpcResultFactory::getRpcCommandResult(
                    status: RpcResultStatus::ERROR,
                    result: [
                        'code' => -32000,
                        'message' => 'Internal server error',
                    ]
                ),
                'expectedExceptionClass' => InternalServerErrorException::class,
                'expectedExceptionMessage' => 'Internal server error',
                'expectedExceptionData' => null,
            ],
            'InvalidRequestException' => [
                'rpcResult' => RpcResultFactory::getRpcCommandResult(
                    status: RpcResultStatus::ERROR,
                    result: [
                        'code' => -32600,
                        'message' => 'Invalid Request',
                        'data' => ['id' => 'Invalid request id'],
                    ]
                ),
                'expectedExceptionClass' => InvalidRequestException::class,
                'expectedExceptionMessage' => 'Invalid Request',
                'expectedExceptionData' => ['id' => 'Invalid request id'],
            ],
            'unknownErrorCode' => [
                'rpcResult' => RpcResultFactory::getRpcCommandResult(
                    status: RpcResultStatus::ERROR,
                    result: [
                        'code' => 123,
                        'message' => 'Unknown error',
                    ]
                ),
                'expectedExceptionClass' => null,
                'expectedExceptionData' => null,
            ],
        ];
    }
}
