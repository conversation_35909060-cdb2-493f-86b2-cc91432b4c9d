<?php

declare(strict_types=1);

namespace App\Tests\Integration\Infrastructure\Domain\Model\Organization;

use App\Domain\Model\Organization\Organization;
use App\Infrastructure\Domain\Model\Organization\DoctrineOrganizationRepository;
use App\Tests\Integration\IntegrationTestCase;

class DoctrineOrganizationRepositoryTest extends IntegrationTestCase
{
    private DoctrineOrganizationRepository $repository;

    protected function setUp(): void
    {
        parent::setUp();

        /** @var DoctrineOrganizationRepository $repository */
        $repository = self::getContainer()->get(DoctrineOrganizationRepository::class);
        $this->repository = $repository;
    }

    public function testFindByNameExcludesSoftDeletedRecords(): void
    {
        // Find an existing organization
        $organization = $this->repository->findByName('Test Organization');
        $this->assertNotNull($organization, 'Organization should exist');
        $this->assertNull($organization->getDeletedAt(), 'Organization should not be soft-deleted initially');

        // Soft delete the organization
        $organization->markAsDeleted();
        $this->repository->save($organization);

        // Clear entity manager to ensure fresh query
        $em = $this->getContainer()->get('doctrine')->getManager();
        $em->clear();

        // Try to find the soft-deleted organization - should return null
        $foundOrganization = $this->repository->findByName('Test Organization');
        $this->assertNull($foundOrganization, 'Should not find soft-deleted organization with findByName');
    }

    public function testFindByEmailExcludesSoftDeletedRecords(): void
    {
        // Find an existing organization
        $organization = $this->repository->findByEmail('<EMAIL>');
        $this->assertNotNull($organization, 'Organization should exist');
        $this->assertNull($organization->getDeletedAt(), 'Organization should not be soft-deleted initially');

        // Soft delete the organization
        $organization->markAsDeleted();
        $this->repository->save($organization);

        // Clear entity manager to ensure fresh query
        $em = $this->getContainer()->get('doctrine')->getManager();
        $em->clear();

        // Try to find the soft-deleted organization - should return null
        $foundOrganization = $this->repository->findByEmail('<EMAIL>');
        $this->assertNull($foundOrganization, 'Should not find soft-deleted organization with findByEmail');
    }

    public function testFindByPhoneExcludesSoftDeletedRecords(): void
    {
        // Find an existing organization
        $organization = $this->repository->findByPhone('+1234567890');
        $this->assertNotNull($organization, 'Organization should exist');
        $this->assertNull($organization->getDeletedAt(), 'Organization should not be soft-deleted initially');

        // Soft delete the organization
        $organization->markAsDeleted();
        $this->repository->save($organization);

        // Clear entity manager to ensure fresh query
        $em = $this->getContainer()->get('doctrine')->getManager();
        $em->clear();

        // Try to find the soft-deleted organization - should return null
        $foundOrganization = $this->repository->findByPhone('+1234567890');
        $this->assertNull($foundOrganization, 'Should not find soft-deleted organization with findByPhone');
    }

    public function testFindExcludesSoftDeletedRecords(): void
    {
        // Find an existing organization
        $organization = $this->repository->findByName('Test Organization');
        $this->assertNotNull($organization, 'Organization should exist');
        $organizationId = $organization->getId();

        // Soft delete the organization
        $organization->markAsDeleted();
        $this->repository->save($organization);

        // Clear entity manager to ensure fresh query
        $em = $this->getContainer()->get('doctrine')->getManager();
        $em->clear();

        // Try to find the soft-deleted organization - should return null
        $foundOrganization = $this->repository->find($organizationId);
        $this->assertNull($foundOrganization, 'Should not find soft-deleted organization with find');
    }

    public function testFindAllExcludesSoftDeletedRecords(): void
    {
        // Get initial count
        $initialOrganizations = $this->repository->findAll();
        $initialCount = count($initialOrganizations);

        // Find an existing organization and soft delete it
        $organization = $this->repository->findByName('Test Organization');
        $this->assertNotNull($organization, 'Organization should exist');

        $organization->markAsDeleted();
        $this->repository->save($organization);

        // Clear entity manager to ensure fresh query
        $em = $this->getContainer()->get('doctrine')->getManager();
        $em->clear();

        // Get organizations after soft delete
        $organizationsAfterDelete = $this->repository->findAll();
        $countAfterDelete = count($organizationsAfterDelete);

        // Should have one less organization
        $this->assertSame($initialCount - 1, $countAfterDelete, 'findAll should exclude soft-deleted organizations');
    }

    public function testCanStillFindSoftDeletedRecordsWithDQL(): void
    {
        // Find an existing organization
        $organization = $this->repository->findByName('Test Organization');
        $this->assertNotNull($organization, 'Organization should exist');
        $organizationId = $organization->getId();

        // Soft delete the organization
        $organization->markAsDeleted();
        $this->repository->save($organization);

        // Clear entity manager to ensure fresh query
        $em = $this->getContainer()->get('doctrine')->getManager();
        $em->clear();

        // Verify repository methods don't find it
        $foundOrganization = $this->repository->find($organizationId);
        $this->assertNull($foundOrganization, 'Repository should not find soft-deleted organization');

        // Disable the soft-deletable filter to find soft-deleted entities
        $em->getFilters()->disable('softdeleteable');

        $qb = $em->createQueryBuilder();
        $foundOrganization = $qb->select('o')
            ->from(Organization::class, 'o')
            ->where('o.id = :id')
            ->setParameter('id', $organizationId)
            ->getQuery()
            ->getOneOrNullResult();

        // Re-enable the filter
        $em->getFilters()->enable('softdeleteable');

        $this->assertInstanceOf(Organization::class, $foundOrganization, 'Should find soft-deleted organization with DQL');
        $this->assertNotNull($foundOrganization->getDeletedAt(), 'Organization should have deletedAt set');
    }
}
