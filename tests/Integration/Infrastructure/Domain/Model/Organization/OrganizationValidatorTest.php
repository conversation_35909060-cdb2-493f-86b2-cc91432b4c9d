<?php

declare(strict_types=1);

namespace App\Tests\Integration\Infrastructure\Domain\Model\Organization;

use App\Domain\Model\Error\ConstraintViolation;
use App\Domain\Model\Error\ValidatorInterface;
use App\Infrastructure\Domain\Model\Organization\OrganizationValidator;
use App\Tests\Integration\IntegrationTestCase;
use App\Tests\Shared\Factory\OrganizationFactory;
use App\Tests\Shared\Random\Generator;
use App\Tests\Shared\Trait\Assertions\ValidationAssertionsTrait;

final class OrganizationValidatorTest extends IntegrationTestCase
{
    use ValidationAssertionsTrait;

    private const CORRECT_DATA = [
        'name' => OrganizationFactory::NAME,
        'address' => OrganizationFactory::ADDRESS,
        'email' => OrganizationFactory::EMAIL,
        'phone' => OrganizationFactory::PHONE,
    ];

    private OrganizationValidator $validator;

    protected function setUp(): void
    {
        parent::setUp();
        /** @var OrganizationValidator $validator */
        $validator = self::getContainer()->get(OrganizationValidator::class);
        $this->validator = $validator;
    }

    /**
     * @dataProvider provideValidData
     */
    public function testValidateWithValidData(array $data): void
    {
        $this->assertNoErrors($this->validator->validate($data));
    }

    /**
     * @dataProvider provideInvalidData
     */
    public function testValidateWithInvalidData(array $data, ?string $group, array $expectedErrors): void
    {
        $this->assertErrors($expectedErrors, $this->validator->validate($data, $group));
    }

    public function testValidateOrganizationNameUniquenessReturnError(): void
    {
        /** @var ConstraintViolation $error */
        $error = $this->validator->validateNameUniqueness(OrganizationFactory::NAME);

        $this->assertNotNull($error);
        $this->assertInstanceOf(ConstraintViolation::class, $error);
        $this->assertSame('name', $error->getPath());
        $this->assertSame('Name Test Organization already exists', $error->getMessage());
    }

    public function testValidateOrganizationNameUniquenessReturnNull(): void
    {
        $error = $this->validator->validateNameUniqueness(OrganizationFactory::NON_EXISTING_NAME);

        $this->assertNull($error);
    }

    public function provideValidData(): array
    {
        return [
            'correctData' => [self::CORRECT_DATA],
        ];
    }

    public function provideInvalidData(): array
    {
        $correctData = self::CORRECT_DATA;

        $wrongEmailData = $correctData;
        $wrongEmailData['email'] = 'wrong@email';

        $wrongPhoneData = $correctData;
        $wrongPhoneData['phone'] = 'a9345897';

        $wrongMaxStringLengthData = $correctData;
        $wrongMaxStringLengthData['name'] = Generator::string(105);
        $wrongMaxStringLengthData['phone'] = '+****************-8901-2345-6789'; // 33 chars, valid format but too long
        $wrongMaxStringLengthData['email'] = '<EMAIL>'; // Keep email valid to avoid multiple errors
        $wrongMaxStringLengthData['address'] = Generator::string(260);

        return [
            'emptyData' => [
                [],
                null,
                [
                    ['path' => 'name', 'message' => 'This field is missing.'],
                    ['path' => 'email', 'message' => 'This field is missing.'],
                    ['path' => 'address', 'message' => 'This field is missing.'],
                ],
            ],
            'emptyDataWithGroupCreate' => [
                [],
                ValidatorInterface::GROUP_CREATE,
                [
                    ['path' => 'name', 'message' => 'This field is missing.'],
                    ['path' => 'email', 'message' => 'This field is missing.'],
                    ['path' => 'address', 'message' => 'This field is missing.'],
                ],
            ],
            'wrongEmail' => [
                $wrongEmailData,
                null,
                [['path' => 'email', 'message' => 'This value is not a valid email address.']],
            ],
            'wrongPhone' => [
                $wrongPhoneData,
                null,
                [['path' => 'phone', 'message' => 'Invalid phone number.']],
            ],
            'wrongMinStringLength' => [
                $wrongMaxStringLengthData,
                null,
                [
                    ['path' => 'name', 'message' => 'This value is too long. It should have 100 characters or less.'],
                    ['path' => 'phone', 'message' => 'This value is too long. It should have 30 characters or less.'],
                    ['path' => 'address', 'message' => 'This value is too long. It should have 255 characters or less.'],
                ],
            ],
        ];
    }
}
