<?php

declare(strict_types=1);

namespace App\Tests\Integration\Infrastructure\Domain\Model\Company;

use App\Domain\Model\Company\Company;
use App\Domain\Model\Organization\Organization;
use App\Infrastructure\Domain\Model\Company\DoctrineCompanyRepository;
use App\Tests\Integration\IntegrationTestCase;
use App\Tests\Shared\Factory\CompanyFactory;

class DoctrineCompanyRepositoryTest extends IntegrationTestCase
{
    private DoctrineCompanyRepository $repository;
    private Organization $organization;

    public function testSaveAndFind(): void
    {
        // Create a new company with test data
        $companyData = [
            'name' => 'New Test Company ' . uniqid(),
            'email' => 'new-' . uniqid() . '@example.com',
            'phone' => '+1' . mt_rand(1000000000, 9999999999),
            'address' => '123 New St, Test City'
        ];

        $company = CompanyFactory::create($this->organization, $companyData);

        // Save the company
        $this->repository->save($company);
        $companyId = $company->getId();

        // Verify the ID is a valid UUID
        $this->assertMatchesRegularExpression(
            '/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i',
            $companyId,
            'Company ID should be a valid UUID v4'
        );

        // Clear the entity manager to ensure we're loading from the database
        $em = $this->getContainer()->get('doctrine')->getManager();
        $em->clear();

        // Find the company by ID
        $foundCompany = $this->repository->find($companyId);

        // Assert the company was found and has the expected values
        $this->assertInstanceOf(Company::class, $foundCompany);
        $this->assertSame($companyId, $foundCompany->getId());
        $this->assertSame($companyData['name'], $foundCompany->getName());
        $this->assertSame($companyData['email'], $foundCompany->getEmail());
        $this->assertSame($companyData['phone'], $foundCompany->getPhone());
        $this->assertSame($companyData['address'], $foundCompany->getAddress());
        $this->assertSame('en', $foundCompany->getDefaultLanguage());
        $this->assertSame('USD', $foundCompany->getDefaultCurrency());
        $this->assertNull($foundCompany->getDeletedAt());
    }

    public function testFindByName(): void
    {
        $company = $this->repository->findByName('Test Property Company');

        $this->assertInstanceOf(Company::class, $company);
        $this->assertSame('Test Property Company', $company->getName());
    }

    public function testFindByEmail(): void
    {
        $company = $this->repository->findByEmail('<EMAIL>');

        $this->assertInstanceOf(Company::class, $company);
        $this->assertSame('<EMAIL>', $company->getEmail());
    }

    public function testFindByPhone(): void
    {
        $company = $this->repository->findByPhone('+1234567890');

        $this->assertInstanceOf(Company::class, $company);
        $this->assertSame('+1234567890', $company->getPhone());
    }

    public function testFindByOrganizationId(): void
    {
        $companies = $this->repository->findByOrganizationId($this->organization->getId());

        // There should be 2 companies for this organization (one is soft-deleted)
        $this->assertCount(2, $companies);
        $this->assertContainsOnlyInstancesOf(Company::class, $companies);
    }

    public function testFindAll(): void
    {
        $companies = $this->repository->findAll();

        // There should be 2 non-deleted companies in the fixtures
        $this->assertCount(2, $companies);
        $this->assertContainsOnlyInstancesOf(Company::class, $companies);
    }

    public function testRemove(): void
    {
        $company = $this->repository->findByName('Test Property Company');
        $companyId = $company->getId();

        $this->repository->remove($company);

        $em = $this->getContainer()->get('doctrine')->getManager();
        $em->flush();
        $em->clear();

        $foundCompany = $this->repository->find($companyId);
        $this->assertNull($foundCompany);
    }

    public function testSoftDelete(): void
    {
        $company = $this->repository->findByName('Second Property Company');
        $this->assertNotNull($company, 'Company should exist');
        $this->assertNull($company->getDeletedAt(), 'Company should not be soft-deleted initially');

        $company->markAsDeleted();
        $this->repository->save($company);

        $em = $this->getContainer()->get('doctrine')->getManager();
        $em->clear();

        $foundCompany = $this->repository->find($company->getId());
        $this->assertNull($foundCompany, 'Should not find soft-deleted company with repository find');

        // Disable the soft-deletable filter to find soft-deleted entities
        $em->getFilters()->disable('softdeleteable');

        $qb = $em->createQueryBuilder();
        $foundCompany = $qb->select('c')
            ->from(Company::class, 'c')
            ->where('c.id = :id')
            ->setParameter('id', $company->getId())
            ->getQuery()
            ->getOneOrNullResult();

        // Re-enable the filter
        $em->getFilters()->enable('softdeleteable');

        $this->assertInstanceOf(Company::class, $foundCompany, 'Should find soft-deleted company with DQL');
        $this->assertNotNull($foundCompany->getDeletedAt(), 'Company should have deletedAt set');
    }

    protected function setUp(): void
    {
        parent::setUp();

        /** @var DoctrineCompanyRepository $repository */
        $repository = self::getContainer()->get(DoctrineCompanyRepository::class);
        $this->repository = $repository;

        $this->organization = $this->getContainer()->get('doctrine')
            ->getRepository(Organization::class)
            ->findOneBy(['name' => 'Test Organization']);
    }
}
