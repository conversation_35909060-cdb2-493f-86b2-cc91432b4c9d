<?php

declare(strict_types=1);

namespace App\Tests\Integration\Infrastructure\Domain\Model\Test;

use App\Domain\Model\Test\Test;
use App\Infrastructure\Domain\Model\Test\DoctrineTestRepository;
use App\Tests\Integration\IntegrationTestCase;
use App\Tests\Shared\Factory\TestFactory;

class DoctrineTestRepositoryTest extends IntegrationTestCase
{
    private DoctrineTestRepository $repository;

    protected function setUp(): void
    {
        parent::setUp();

        /** @var DoctrineTestRepository $repo */
        $repo = self::getContainer()->get(DoctrineTestRepository::class);
        $this->repository = $repo;
    }

    public function testSaveAndFindOneById(): void
    {
        $test = new Test('myValue');
        $this->repository->save($test);

        $foundValue = $this->repository->findOneById($test->getId());

        $this->assertNotNull($foundValue);
        $this->assertSame('myValue', $foundValue->getValue());
    }

    public function testFindOneByValue(): void
    {
        $foundValue = $this->repository->findOneByValue(TestFactory::EXISTS_TEST_VALUE);

        $this->assertInstanceOf(Test::class, $foundValue);
        $this->assertSame(TestFactory::EXISTS_TEST_VALUE, $foundValue->getValue());
    }

    public function testFindAll(): void
    {
        $all = $this->repository->findAll();

        $this->assertCount(5, $all);
        $this->assertContainsOnlyInstancesOf(Test::class, $all);
    }
}
