<?php

declare(strict_types=1);

namespace App\Tests\Integration\Infrastructure\Service\Mail;

use App\Domain\Model\Email\EmailConfig;
use App\Infrastructure\Service\Mail\MailSender;
use App\Tests\Integration\IntegrationTestCase;
use App\Tests\Shared\Factory\EmailConfigFactory;
use App\Tests\Shared\Factory\MailFactory;
use App\Tests\Shared\Trait\Assertions\EmailAssertionsTrait;
use Symfony\Bundle\FrameworkBundle\Test\MailerAssertionsTrait;
use Symfony\Component\Mime\Exception\RfcComplianceException;
use Twig\Environment;

final class MailSenderTest extends IntegrationTestCase
{
    use EmailAssertionsTrait;
    use MailerAssertionsTrait;

    private MailSender $mailSender;
    private Environment $twigRenderer;

    protected function setUp(): void
    {
        parent::setUp();
        /** @var MailSender $mailSender */
        $mailSender = self::getContainer()->get(MailSender::class);
        $this->mailSender = $mailSender;

        /** @var Environment $twigRenderer */
        $twigRenderer = self::getContainer()->get(Environment::class);
        $this->twigRenderer = $twigRenderer;
    }

    public function testSendWithTemplateFromString(): void
    {
        $config = new EmailConfig(
            to: MailFactory::TEST_RECIPIENT,
            bcc: null,
            from: EmailConfigFactory::TEST_FROM_EMAIL,
            subjectTemplate: MailFactory::SUBJECT_NEW,
            subjectData: MailFactory::SUBJECT_TEST_DATA_NEW,
            bodyTemplate: MailFactory::BODY_NEW,
            bodyData: MailFactory::BODY_TEST_DATA_NEW,
        );

        $this->mailSender->send($config);

        $this->assertEmailCount(1);
        $this->assertEmail($config);
    }

    public function testSendWithTemplatesFromFile(): void
    {
        $config = new EmailConfig(
            to: MailFactory::TEST_RECIPIENT,
            bcc: MailFactory::TEST_RECIPIENTS,
            from: EmailConfigFactory::TEST_FROM_EMAIL,
            subjectTemplate: MailFactory::SUBJECT_NEW,
            subjectData: MailFactory::SUBJECT_TEST_DATA_NEW,
            bodyTemplate: MailFactory::BODY_NEW,
            bodyData: MailFactory::BODY_TEST_DATA_NEW,
        );

        $this->mailSender->send($config, true);

        $this->assertEmailCount(1);
        $this->assertEmail($config, true);
    }

    public function testSendWithInvalidEmail(): void
    {
        $this->expectException(RfcComplianceException::class);

        $this->mailSender->send(
            new EmailConfig(
                to: 'invalid_email',
                bcc: null,
                from: EmailConfigFactory::TEST_FROM_EMAIL,
                subjectTemplate: MailFactory::SUBJECT_NEW,
                subjectData: MailFactory::SUBJECT_TEST_DATA_NEW,
                bodyTemplate: MailFactory::BODY_NEW,
                bodyData: MailFactory::BODY_TEST_DATA_NEW,
            )
        );
    }
}
