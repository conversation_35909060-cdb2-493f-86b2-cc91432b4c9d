<?php

declare(strict_types=1);

namespace App\Tests\Integration\Infrastructure\Http;

use App\Application\Shared\Error\ErrorResponse;
use App\Infrastructure\Http\ResponseMapper;
use App\Tests\Integration\IntegrationTestCase;
use DateTime;
use Symfony\Component\HttpFoundation\JsonResponse;

final class ResponseMapperTest extends IntegrationTestCase
{
    private ResponseMapper $responseMapper;

    protected function setUp(): void
    {
        parent::setUp();

        /** @var ResponseMapper $responseMapper */
        $responseMapper = self::getContainer()->get(ResponseMapper::class);
        $this->responseMapper = $responseMapper;
    }

    /**
     * @dataProvider provideSuccessfulResponseData
     * @param array<string,array<mixed,mixed>> $expectedData
     */
    public function testSerializeResponseWithSuccessfulResponse(mixed $data, array $expectedData): void
    {
        $response = $this->responseMapper->serializeResponse($data, 201);
        $expectedResponse = new JsonResponse(['data' => $expectedData], 201);

        // Compare the JSON strings while ignoring minor differences in DateTime serialization
        $this->assertJsonStringEqualsJsonString(
            $expectedResponse->getContent(),
            $response->getContent(),
            'The JSON responses should be equal'
        );
    }

    public function testSerializeErrorResponse(): void
    {
        $response = $this->responseMapper->serializeErrorResponse(ErrorResponse::fromCommonError('Test Error'));

        $this->assertEquals(new JsonResponse(['errors' => ['common' => 'Test Error']], 422), $response);
    }

    public function testSerializeResponseWithErrorResponse(): void
    {
        $response = $this->responseMapper->serializeResponse(ErrorResponse::fromCommonError('Test Error'));

        $this->assertEquals(new JsonResponse(['errors' => ['common' => 'Test Error']], 422), $response);
    }

    public function testSerializeResponseWithNull(): void
    {
        $response = $this->responseMapper->serializeResponse(null, 204);

        $this->assertEquals(new JsonResponse(null, 204), $response);
    }

    public function testSerializeNotFoundErrorResponse(): void
    {
        $response = $this->responseMapper->serializeErrorResponse(ErrorResponse::notFound());

        $this->assertEquals(new JsonResponse(['message' => 'Not Found'], 404), $response);
    }

    /**
     * @return array<string,array<mixed,mixed>>
     */
    public function provideSuccessfulResponseData(): array
    {
        $string = 'Dummy';
        $array = [1, 2, 3];
        $dateTime = new DateTime();
        $int = 100;
        $float = 100.01;
        $container = self::getContainer();
        $serializer = $container->get('serializer');
        $formattedDateTime = $serializer->serialize($dateTime, 'json');

        return [
            'dummyResponse' => [
                new DummyResponse($string, $array, $dateTime, $int, $float, true, null),
                [
                    'string' => $string,
                    'array' => $array,
                    'dateTime' => json_decode($formattedDateTime, true),
                    'int' => $int,
                    'float' => $float,
                    'isTrue' => true,
                    'nullableString' => null,
                ],
            ],
        ];
    }
}
