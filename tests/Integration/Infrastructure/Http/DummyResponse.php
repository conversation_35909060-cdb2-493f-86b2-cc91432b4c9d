<?php

declare(strict_types=1);

namespace App\Tests\Integration\Infrastructure\Http;

final class DummyResponse
{
    /**
     * @param array<mixed,mixed> $array
     */
    public function __construct(
        public string $string,
        public array $array,
        public \DateTime $dateTime,
        public int $int,
        public float $float,
        public bool $isTrue,
        public ?string $nullableString = null,
    ) {
    }

    public function getString(): string
    {
        return $this->string;
    }

    /**
     * @return array<mixed,mixed>
     */
    public function getArray(): array
    {
        return $this->array;
    }

    public function getDateTime(): \DateTime
    {
        return $this->dateTime;
    }

    public function getInt(): int
    {
        return $this->int;
    }

    public function getFloat(): float
    {
        return $this->float;
    }

    public function getIsTrue(): bool
    {
        return $this->isTrue;
    }

    public function getNullableString(): ?string
    {
        return $this->nullableString;
    }
}
