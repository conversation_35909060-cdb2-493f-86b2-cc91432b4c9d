<?php

declare(strict_types=1);

namespace App\Tests\Integration\Application\Command\Organization\Create;

use App\Application\Command\Organization\Create\CreateOrganizationCommand;
use App\Application\Command\Organization\Create\CreateOrganizationCommandHandler;
use App\Application\Shared\Organization\OrganizationResponse;
use App\Application\Shared\Error\ErrorResponse;
use App\Tests\Integration\IntegrationTestCase;
use App\Tests\Shared\Factory\OrganizationFactory;

final class CreateOrganizationCommandHandlerTest extends IntegrationTestCase
{
    private CreateOrganizationCommandHandler $handler;

    protected function setUp(): void
    {
        parent::setUp();

        /** @var CreateOrganizationCommandHandler $handler */
        $handler = self::getContainer()->get(CreateOrganizationCommandHandler::class);
        $this->handler = $handler;
    }

    public function testCreateOrganizationWithCorrectDataReturnResult(): void
    {
        $command = new CreateOrganizationCommand(
            OrganizationFactory::NON_EXISTING_NAME,
            OrganizationFactory::EMAIL,
            OrganizationFactory::PHONE,
            OrganizationFactory::ADDRESS
        );

        $result = $this->handler->__invoke($command);

        $this->assertInstanceOf(OrganizationResponse::class, $result);
        $this->assertSame(OrganizationFactory::NON_EXISTING_NAME, $result->getName());
        $this->assertSame(OrganizationFactory::EMAIL, $result->getEmail());
        $this->assertSame(OrganizationFactory::PHONE, $result->getPhone());
        $this->assertSame(OrganizationFactory::ADDRESS, $result->getAddress());
    }

    public function testCreateOrganizationWithIncorrectDataReturnError(): void
    {
        $command = new CreateOrganizationCommand(
            '', // Empty name should cause validation error
            'invalid-email', // Invalid email format
            'invalid-phone', // Invalid phone format
            '' // Empty address should cause validation error
        );

        $result = $this->handler->__invoke($command);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertNotEmpty($result->getErrors());
    }
}
