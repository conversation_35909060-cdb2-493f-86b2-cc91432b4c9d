<?php
declare(strict_types=1);

namespace App\Tests\Integration\Application\Command\Test\SendMail;

use App\Application\Command\Test\SendMail\SendTestMailCommand;
use App\Application\Command\Test\SendMail\SendTestMailCommandHandler;
use App\Application\Service\Mail\MailSenderInterface;
use App\Tests\Integration\IntegrationTestCase;
use Symfony\Bundle\FrameworkBundle\Test\MailerAssertionsTrait;

class SendTestMailCommandHandlerTest extends IntegrationTestCase
{
    use MailerAssertionsTrait;
    private SendTestMailCommandHandler $handler;
    /** @phpstan-ignore property.onlyWritten */
    private MailSenderInterface $mailSender;

    protected function setUp(): void
    {
        parent::setUp();
        self::bootKernel();

        /** @var MailSenderInterface $mailSender */
        $mailSender = self::getContainer()->get(MailSenderInterface::class);
        $this->mailSender = $mailSender;

        /** @var SendTestMailCommandHandler $handler */
        $handler = self::getContainer()->get(SendTestMailCommandHandler::class);
        $this->handler = $handler;
    }

    public function testHandleCommand(): void
    {
        // Execute the command
        ($this->handler)(new SendTestMailCommand());

        $this->assertEmailCount(1);
    }
}
