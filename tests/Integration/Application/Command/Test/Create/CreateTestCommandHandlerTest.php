<?php
declare(strict_types=1);

namespace App\Tests\Integration\Application\Command\Test\Create;

use App\Application\Command\Test\Create\CreateTestCommand;
use App\Application\Command\Test\Create\CreateTestCommandHandler;
use App\Domain\Model\Test\TestRepositoryInterface;
use App\Tests\Integration\IntegrationTestCase;

class CreateTestCommandHandlerTest extends IntegrationTestCase
{
    private CreateTestCommandHandler $handler;
    private TestRepositoryInterface $testRepository;

    protected function setUp(): void
    {
        parent::setUp();
        self::bootKernel();

        /** @var TestRepositoryInterface $testRepository */
        $testRepository = self::getContainer()->get(TestRepositoryInterface::class);
        $this->testRepository = $testRepository;

        /** @var CreateTestCommandHandler $handler */
        $handler = self::getContainer()->get(CreateTestCommandHandler::class);
        $this->handler = $handler;
    }

    public function testHandleCommand(): void
    {
        $value = 'test-value';
        $command = new CreateTestCommand($value);

        // Execute the command
        ($this->handler)($command);

        // Verify the test was created
        $test = $this->testRepository->findOneByValue($value);
        $this->assertNotNull($test);
        $this->assertEquals($value, $test->getValue());
    }
}
