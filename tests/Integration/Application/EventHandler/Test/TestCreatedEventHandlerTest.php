<?php

declare(strict_types=1);

namespace App\Tests\Integration\Application\EventHandler\Test;

use App\Application\EventHandler\Test\TestCreatedEventHandler;
use App\Domain\Model\Test\TestCreatedEvent;
use App\Tests\Integration\IntegrationTestCase;

class TestCreatedEventHandlerTest extends IntegrationTestCase
{
    private TestCreatedEventHandler $handler;
    protected function setUp(): void
    {
        parent::setUp();
        $this->handler = self::getContainer()->get(TestCreatedEventHandler::class);
    }

    public function testHandlesTestCreatedEvent(): void
    {
        $testName = 'Integration Test';
        $event = new TestCreatedEvent($testName);

        $this->handler->__invoke($event);
        $this->assertLog('WARNING', "From Event: $testName");
    }
}
