<?php
declare(strict_types=1);

namespace App\Tests\Integration\Application\Query\Test\Get;

use App\Application\Query\Test\Get\GetTestQuery;
use App\Application\Query\Test\Get\GetTestQueryHandler;
use App\Application\Query\Test\Get\GetTestQueryResponse;
use App\Domain\Model\Test\Test;
use App\Domain\Model\Test\TestRepositoryInterface;
use App\Tests\Integration\IntegrationTestCase;

class GetTestQueryHandlerTest extends IntegrationTestCase
{
    private GetTestQueryHandler $handler;
    private TestRepositoryInterface $repository;

    protected function setUp(): void
    {
        parent::setUp();
        self::bootKernel();

        /** @var TestRepositoryInterface $repository */
        $repository = self::getContainer()->get(TestRepositoryInterface::class);
        $this->repository = $repository;

        /** @var GetTestQueryHandler $handler */
        $handler = self::getContainer()->get(GetTestQueryHandler::class);
        $this->handler = $handler;
    }

    public function testHandleExistingTest(): void
    {
        $value = 'test-value';
        $createdAt = new \DateTimeImmutable();

        $test = new Test($value);
        $test->setCreatedAt($createdAt);
        $this->repository->save($test);

        $response = ($this->handler)(new GetTestQuery((string) $test->getId()));

        $this->assertInstanceOf(GetTestQueryResponse::class, $response);
        $this->assertEquals($test->getId(), $response->getId());
        $this->assertEquals($value, $response->getValue());
        $this->assertEquals($createdAt->format('Y-m-d H:i:s'), $response->getCreatedAt());
    }

    public function testHandleNonExistingTest(): void
    {
        $testId = '20';
        $response = ($this->handler)(new GetTestQuery((string) $testId));
        $this->assertNull($response);
    }
}
