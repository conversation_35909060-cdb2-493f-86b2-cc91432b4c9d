<?php

declare(strict_types=1);

namespace App\Tests\Integration\Application\Query\Test\GetAll;

use App\Application\Query\Test\GetAll\GetAllTestsQuery;
use App\Application\Query\Test\GetAll\GetAllTestsQueryHandler;
use App\Application\Query\Test\GetAll\GetAllTestsQueryResponse;
use App\Domain\Model\Test\Test;
use App\Domain\Model\Test\TestRepositoryInterface;
use App\Infrastructure\Rpc\RpcCommand;
use App\Tests\Integration\IntegrationTestCase;
use App\Tests\Shared\Factory\RpcResultFactory;

class GetAllTestsQueryHandlerTest extends IntegrationTestCase
{
    private GetAllTestsQueryHandler $handler;
    private TestRepositoryInterface $repository;

    protected function setUp(): void
    {
        parent::setUp();
        self::bootKernel();

        /** @var TestRepositoryInterface $repository */
        $repository = self::getContainer()->get(TestRepositoryInterface::class);
        $this->repository = $repository;

        /** @var GetAllTestsQueryHandler $handler */
        $handler = self::getContainer()->get(GetAllTestsQueryHandler::class);
        $this->handler = $handler;
    }

    public function testHandleQuery(): void
    {
        $this->mockRpcResponse(
            function (RpcCommand $rpcCommand) {
                if ('tenants.getSingleTest' !== $rpcCommand->getCommand()) {
                    return false;
                }
                return '7' === $rpcCommand->getArguments()[0]->getTestId();
            },
            RpcResultFactory::getRpcCommandResult(result: [
                'id' => '7',
                'value' => 'Test 7',
            ]),
        );

        // Create tests
        $test1 = new Test('Test 1');
        $test2 = new Test('Test 2');
        $this->repository->save($test1);
        $this->repository->save($test2);

        // Execute handler
        $response = ($this->handler)(new GetAllTestsQuery());

        // Verify response
        $this->assertInstanceOf(GetAllTestsQueryResponse::class, $response);
        $this->assertCount(7, $response->getTests());
    }
}
