<?php

declare(strict_types=1);

namespace App\Tests\Integration;

use App\Infrastructure\Rpc\RpcResult;
use App\Infrastructure\Rpc\Transport\RpcResultReceiverInterface;
use App\Tests\Double\Rpc\Transport\MockResultReceiver;
use Closure;
use Coduo\PHPMatcher\PHPUnit\PHPMatcherAssertions;
use Doctrine\Common\DataFixtures\Executor\ORMExecutor;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Tools\SchemaTool;
use Doctrine\Common\DataFixtures\Purger\ORMPurger;
use Doctrine\Bundle\FixturesBundle\Loader\SymfonyFixturesLoader;
use RuntimeException;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Monolog\Handler\TestHandler;

abstract class IntegrationTestCase extends KernelTestCase
{
    use PHPMatcherAssertions;

    /** @var ?EntityManagerInterface $em */
    protected ?EntityManagerInterface $em = null;

    protected function setUp(): void
    {
        parent::setUp();
        self::bootKernel();

        /** @var EntityManagerInterface $entityManager */
        $entityManager = self::getContainer()->get(EntityManagerInterface::class);
        $this->em = $entityManager;

        $this->em->getConnection()->beginTransaction();
    }

    protected function tearDown(): void
    {
        if ($this->em !== null) {
            if ($this->em->getConnection()->isTransactionActive()) {
                $this->em->getConnection()->rollBack();
            }
            $this->em->close();
            $this->em = null;
        }

        parent::tearDown();
        static::ensureKernelShutdown();
    }

    protected function resetDatabaseSchema(): void
    {
        $metadata = $this->em->getMetadataFactory()->getAllMetadata();
        if (!empty($metadata)) {
            $tool = new SchemaTool($this->em);
            $tool->dropSchema($metadata);
            $tool->createSchema($metadata);
        }
    }

    protected function loadFixtures(): void
    {
        $loader = new SymfonyFixturesLoader();
        $loader->loadFromDirectory(__DIR__ . '/../shared/Fixtures');

        $executor = new ORMExecutor($this->em, new ORMPurger());
        $executor->execute($loader->getFixtures());
    }

    /**
     * @param array<mixed,mixed> $entities
     */
    protected function truncateEntities(array $entities): void
    {
        $connection = $this->em->getConnection();
        $platform = $connection->getDatabasePlatform();

        // Make sure we're not inside another transaction before starting a new one
        $wasInTransaction = $connection->isTransactionActive();

        if (!$wasInTransaction) {
            $connection->beginTransaction();
        }

        try {
            $connection->executeStatement('SET FOREIGN_KEY_CHECKS=0');
            foreach ($entities as $entityClass) {
                $cmd = $this->em->getClassMetadata($entityClass);
                $tableName = $cmd->getTableName();
                $connection->executeStatement($platform->getTruncateTableSQL($tableName, true));
            }
            $connection->executeStatement('SET FOREIGN_KEY_CHECKS=1');

            if (!$wasInTransaction) {
                $connection->commit();
            }
        } catch (\Exception $e) {
            if (!$wasInTransaction) {
                $connection->rollBack();
            }
            throw $e;
        }
    }

    protected function mockRpcResponse(Closure $matchCallback, RpcResult $response): void
    {
        $rpcResultReceiver = self::getContainer()->get(RpcResultReceiverInterface::class);

        if (!$rpcResultReceiver instanceof MockResultReceiver) {
            throw new RuntimeException('RpcResultReceiver must be an instance of MockResultReceiver');
        }

        $rpcResultReceiver->mock($matchCallback, $response);
    }

    /**
     * @param array<mixed,mixed> $expectedContext
     */
    protected function assertLog(string $expectedLevel, string $expectedMessage, array $expectedContext = []): void
    {
        /** @var TestHandler $logger */
        $logger = self::getContainer()->get('monolog.handler.test');
        $logs = $logger->getRecords();

        $this->assertCount(1, $logs);
        $this->assertSame($expectedLevel, $logs[0]['level_name']);
        $this->assertSame($expectedMessage, $logs[0]['message']);
        $this->assertMatchesPattern($expectedContext, $logs[0]['context']);
    }
}


