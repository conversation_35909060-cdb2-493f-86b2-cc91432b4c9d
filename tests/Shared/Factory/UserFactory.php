<?php

declare(strict_types=1);

namespace App\Tests\Shared\Factory;

final class UserFactory
{
    public const TEMPLATES_PATH = __DIR__.DIRECTORY_SEPARATOR.'..'.DIRECTORY_SEPARATOR.'..'.DIRECTORY_SEPARATOR.'..'.DIRECTORY_SEPARATOR.'templates'.DIRECTORY_SEPARATOR;
    public const NON_EXISTING_ID = '20cf4ce9-da09-f073-b874-e9b31a8f28ca';
    public const ADMIN_ID = '90cf4989-3281-42e2-b22a-80c5d1523689';

    public const EMAIL = '<EMAIL>';
    public const FIRST_NAME = 'Ivan';
    public const LAST_NAME = 'Georgiev';
    public const PASSWORD = '12345678';
    public const IS_VERIFIED = true;

    public const TOKEN = 'secret';

}
