<?php

declare(strict_types=1);

namespace App\Tests\Shared\Factory;

use App\Domain\Model\Company\Company;
use App\Domain\Model\Organization\Organization;

class CompanyFactory
{
    public const DEFAULT_NAME = 'Test Company';
    public const DEFAULT_EMAIL = '<EMAIL>';
    public const DEFAULT_PHONE = '+1234567890';
    public const DEFAULT_ADDRESS = '123 Test St, Test City';
    public const DEFAULT_LANGUAGE = 'en';
    public const DEFAULT_CURRENCY = 'USD';

    public static function create(Organization $organization, array $overrides = []): Company
    {
        return new Company(
            $organization,
            $overrides['name'] ?? self::DEFAULT_NAME,
            $overrides['email'] ?? self::DEFAULT_EMAIL,
            $overrides['phone'] ?? self::DEFAULT_PHONE,
            $overrides['address'] ?? self::DEFAULT_ADDRESS,
            $overrides['defaultLanguage'] ?? self::DEFAULT_LANGUAGE,
            $overrides['defaultCurrency'] ?? self::DEFAULT_CURRENCY,
            $overrides['id'] ?? null
        );
    }
}
