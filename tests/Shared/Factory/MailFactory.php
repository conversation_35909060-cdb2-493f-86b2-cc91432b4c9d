<?php

declare(strict_types=1);

namespace App\Tests\Shared\Factory;

final class MailFactory
{
    public const TEST_RECIPIENT = '<EMAIL>';
    public const TEST_RECIPIENT_2 = '<EMAIL>';

    public const TEST_RECIPIENTS = [self::TEST_RECIPIENT];
    
    public const SUBJECT_NEW = 'test/emails/en_US/mail/subject.txt.twig';
    public const BODY_NEW = 'test/emails/en_US/mail/body.html.twig';
    
    public const SUBJECT_TEST_DATA_NEW = ['subject' => 'Test Subject'];
    public const BODY_TEST_DATA_NEW = ['body' => 'Test Body'];
    
    public const SUBJECT_DATA = ['domain' => EmailConfigFactory::BACKOFFICE_DOMAIN];
    
    public const COMMON_BODY_DATA = [
        'firstName' => UserFactory::FIRST_NAME,
        'lastName' => UserFactory::LAST_NAME,
        'domain' => EmailConfigFactory::BACKOFFICE_DOMAIN,
    ];
}
