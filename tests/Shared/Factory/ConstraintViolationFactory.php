<?php

declare(strict_types=1);

namespace App\Tests\Shared\Factory;

use App\Domain\Model\Error\ConstraintViolation;

final class ConstraintViolationFactory
{
    public const PATH_COMMON = 'common';
    public const PATH_MESSAGE = 'message';
    public const PATH = 'test';
    public const MESSAGE = 'test message';

    public static function getConstraintViolation(
        string $message = self::MESSAGE,
        string $path = self::PATH
    ): ConstraintViolation {
        return new ConstraintViolation($message, $path);
    }
}
