<?php

declare(strict_types=1);

namespace App\Tests\Shared\Factory;

final class EmailConfigFactory
{
    public const BACKOFFICE_DOMAIN = 'backoffice.obrazcov.test';
    public const TRANSPORT = 'null://';
    public const BACKOFFICE_FROM_EMAIL = 'no-reply@'.self::BACKOFFICE_DOMAIN;
    public const TEST_FROM_EMAIL = '<EMAIL>';
    public const TENANT_FROM_EMAIL = '<EMAIL>';
    public const FROM_NAME = 'OBRACZOV';
}
