<?php

declare(strict_types=1);

namespace App\Tests\Shared\Fixtures;

use App\Domain\Model\Test\Test;
use Doctrine\Bundle\FixturesBundle\Fixture;

class TestFixture extends BaseCsvFixture
{
    protected function getFileName(): string
    {
        return 'test.csv';
    }

    protected function getTableName(): string
    {
        return 'tests';
    }

    /**
     * @return array<mixed>
     */
    protected function getFieldMappings(): array
    {
        return [];
    }

    /**
     * @return array<mixed>
     */
    protected function getTypeCasts(): array
    {
        return [];
    }
}
