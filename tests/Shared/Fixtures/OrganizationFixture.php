<?php

declare(strict_types=1);

namespace App\Tests\Shared\Fixtures;

use App\Domain\Model\Organization\Organization;

class OrganizationFixture extends BaseCsvFixture
{
    protected function getFileName(): string
    {
        return 'organizations.csv';
    }

    protected function getTableName(): string
    {
        return 'organizations';
    }

    protected function getEntityClass(): string
    {
        return Organization::class;
    }

    protected function getReferenceKeyPrefix(): string
    {
        return 'organization-';
    }

    protected function getReferenceColumn(): string
    {
        return 'id';
    }
}
