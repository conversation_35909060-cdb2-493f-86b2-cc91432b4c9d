<?php

declare(strict_types=1);

namespace App\Tests\Shared\Fixtures;

use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\ObjectManager;
use Exception;
use Symfony\Component\PropertyAccess\PropertyAccess;

abstract class BaseCsvFixture extends Fixture
{
    public function load(ObjectManager $manager): void
    {
        $fileName = $this->getFileName();
        $csvPath = __DIR__ . '/../Resources/' . $fileName;

        $tableName = $this->getTableName();

        $fieldMappings = $this->getFieldMappings();
        $typeCasts = $this->getTypeCasts();

        $this->loadFromCsv(
            $manager,
            $csvPath,
            $tableName,
            $fieldMappings,
            $typeCasts
        );
    }

    /**
     * @param array<mixed,mixed> $fieldMappings
     * @param array<mixed,mixed> $typeCasts
     * @throws Exception
     */
    protected function loadFromCsv(
        ObjectManager $manager,
        string $csvPath,
        string $tableName,
        array $fieldMappings = [],
        array $typeCasts = []
    ): void {
        if (!file_exists($csvPath)) {
            throw new \RuntimeException("CSV file not found at $csvPath");
        }

        $handle = fopen($csvPath, 'r');
        $headers = fgetcsv($handle);

        $dbColumns = [];
        foreach ($headers as $header) {
            $dbColumns[] = $fieldMappings[$header] ?? $header;
        }

        $batchSize = 100;
        $batchCount = 0;
        $values = [];
        $params = [];

        while (($row = fgetcsv($handle)) !== false) {
            $rowValues = [];
            $rowData = array_combine($headers, $row);

            foreach ($headers as $index => $header) {
                $column = $fieldMappings[$header] ?? $header;
                $value = $rowData[$header];

                if ($value === '') {
                    $rowValues[] = 'NULL';
                    continue;
                }

                if (isset($typeCasts[$column])) {
                    $value = $this->castValueForDatabase($value, $typeCasts[$column]);
                }

                $paramName = "p" . $batchCount . "_" . $index;
                $rowValues[] = ":" . $paramName;
                $params[$paramName] = $value;
            }

            $values[] = "(" . implode(", ", $rowValues) . ")";
            $batchCount++;

            if ($batchCount >= $batchSize) {
                $this->executeBatchInsert($manager, $tableName, $dbColumns, $values, $params);
                $batchCount = 0;
                $values = [];
                $params = [];
            }
        }

        if ($batchCount > 0) {
            $this->executeBatchInsert($manager, $tableName, $dbColumns, $values, $params);
        }

        fclose($handle);
    }

    /**
     * @param array<mixed,mixed> $columns
     * @param array<mixed,mixed> $params
     * @param array<mixed,mixed> $values
     */
    protected function executeBatchInsert(
        ObjectManager $manager,
        string $tableName,
        array $columns,
        array $values,
        array $params
    ): void {
        if (empty($values)) {
            return;
        }

        /** @phpstan-ignore method.notFound */
        $connection = $manager->getConnection();

        $columnsString = implode(", ", array_map(function($col) use ($connection) {
            return $connection->quoteIdentifier($col);
        }, $columns));

        $valuesString = implode(", ", $values);

        $sql = "INSERT INTO " . $connection->quoteIdentifier($tableName) . " ($columnsString) VALUES $valuesString";

        $stmt = $connection->prepare($sql);

        foreach ($params as $param => $value) {
            $stmt->bindValue($param, $value);
        }

        $stmt->executeStatement();
    }

    protected function castValueForDatabase(string $value, string $type): mixed
    {
        return match ($type) {
            'int'      => (int)$value,
            'float'    => (float)$value,
            'bool'     => filter_var($value, FILTER_VALIDATE_BOOLEAN) ? 1 : 0,
            'datetime' => (new \DateTimeImmutable($value))->format('Y-m-d H:i:s'),
            'date'     => (new \DateTimeImmutable($value))->format('Y-m-d'),
            'time'     => (new \DateTimeImmutable($value))->format('H:i:s'),
            'json'     => json_encode(json_decode($value, true) ?: []),
            default    => $value,
        };
    }

    abstract protected function getFileName(): string;

    abstract protected function getTableName(): string;

    /**
     * @return array<mixed,mixed>
     */
    protected function getFieldMappings(): array
    {
        return [];
    }

    /**
     * @return array<mixed,mixed>
     */
    protected function getTypeCasts(): array
    {
        return [];
    }
}
