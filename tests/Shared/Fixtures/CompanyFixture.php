<?php

declare(strict_types=1);

namespace App\Tests\Shared\Fixtures;

use App\Domain\Model\Company\Company;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;

class CompanyFixture extends BaseCsvFixture implements DependentFixtureInterface
{
    public function getDependencies(): array
    {
        return [
            OrganizationFixture::class,
        ];
    }

    protected function getFileName(): string
    {
        return 'companies.csv';
    }

    protected function getTableName(): string
    {
        return 'companies';
    }

    protected function getEntityClass(): string
    {
        return Company::class;
    }

    protected function getReferenceKeyPrefix(): string
    {
        return 'company-';
    }

    protected function getReferenceColumn(): string
    {
        return 'id';
    }
}
