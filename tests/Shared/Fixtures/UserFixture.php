<?php

declare(strict_types=1);

namespace App\Tests\Shared\Fixtures;

use App\Domain\Model\User\User;
use Doctrine\Persistence\ObjectManager;

class UserFixture extends BaseCsvFixture
{
    protected function getFileName(): string
    {
        return 'users.csv';
    }

    protected function getTableName(): string
    {
        return 'users';
    }

    /**
     * @return array<string,string>
     */
    protected function getFieldMappings(): array
    {
        return [
            'user_email' => 'email',
            'isVerified' => 'is_verified',
        ];
    }

    /**
     * @return array<string,string>
     */
    protected function getTypeCasts(): array
    {
        return [
            'isVerified' => 'bool',
            'createdAt' => 'datetime',
            'updatedAt' => 'datetime',
        ];
    }
}
