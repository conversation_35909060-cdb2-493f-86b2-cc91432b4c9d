<?php

declare(strict_types=1);

namespace App\Tests\Shared\Random;

use Symfony\Component\String\ByteString;
use Symfony\Component\Uid\Uuid;

final class Generator
{
    public static function string(int $length = 10): string
    {
        return ByteString::fromRandom($length, implode('', range('A', 'Z')))->toString();
    }

    public static function email(): string
    {
        return sprintf('%s@%s.com', self::string(), self::string());
    }

    public static function uuid(): string
    {
        return (string)Uuid::v4();
    }

    public static function kebabCaseToCamelCase(string $string): string
    {
        $str = str_replace(' ', '', ucwords(str_replace('-', ' ', $string)));
        $str[0] = strtolower($str[0]);

        return $str;
    }
}
