<?php

declare(strict_types=1);

namespace App\Tests\Shared\Trait\Assertions;

use App\Domain\Model\Email\EmailConfig;
use App\Tests\Shared\Factory\EmailConfigFactory;
use Symfony\Component\Mime\Address;
use Symfony\Component\Mime\Email;

trait EmailAssertionsTrait
{
    protected function assertEmail(EmailConfig $config, bool $useFileTemplates = false, int $index = 0): void
    {
        /** @var Email $email */
        $email = $this->getMailerMessage($index, EmailConfigFactory::TRANSPORT);
        $this->assertInstanceOf(Email::class, $email);

        $from = $config->getFrom();
        if (is_array($from)) {
            $this->assertEquals([new Address($from['email'], $from['name'] ?? '')], $email->getFrom());
        } else {
            $this->assertEquals([new Address($from)], $email->getFrom());
        }

        $to = $config->getTo();
        if (is_array($to)) {
            $this->assertEquals([new Address($to['email'], $to['name'] ?? '')], $email->getTo());
        } else {
            $this->assertEquals([new Address($to)], $email->getTo());
        }

        $bcc = $config->getBcc();
        $formattedBcc = [];
        if (null !== $bcc && [] !== $bcc) {
            foreach ($bcc as $recipient) {
                $formattedBcc[] = is_array($recipient) ? new Address($recipient['email'], $recipient['name'] ?? '') : new Address($recipient);
            }
        }
        $this->assertEquals($formattedBcc, $email->getBcc());

        $this->assertContent($config->getSubjectTemplate(), $config->getSubjectData(), (string) $email->getSubject(), $useFileTemplates);
        $this->assertContent($config->getBodyTemplate(), $config->getBodyData(), (string) $email->getHtmlBody(), $useFileTemplates);
    }

    /**
     * @param string $template
     * @param array<mixed> $data
     * @param string $realContent
     * @param bool $useFileTemplates
     * @return void
     * @throws \Twig\Error\LoaderError
     * @throws \Twig\Error\RuntimeError
     * @throws \Twig\Error\SyntaxError
     */
    protected function assertContent(string $template, array $data, string $realContent, bool $useFileTemplates = false): void
    {
        if ($useFileTemplates) {
            $expectedContent = $this->twigRenderer->render($template, $data);
        } else {
            $expectedContent = $this->twigRenderer->createTemplate($template)->render($data);
        }

        $this->assertSame($expectedContent, $realContent);
    }
}
