###> symfony/framework-bundle ###
/.env.local
/.env.local.php
/.env.*.local
/config/secrets/prod/prod.decrypt.private.php
/public/bundles/
/var/
/vendor/
###< symfony/framework-bundle ###

###> phpunit/phpunit ###
/phpunit.xml
.phpunit.result.cache
###< phpunit/phpunit ###

###> symfony/phpunit-bridge ###
.phpunit.result.cache
/phpunit.xml
###< symfony/phpunit-bridge ###


.idea
docs
###> phpstan/phpstan ###
phpstan.neon
###< phpstan/phpstan ###


###> phpunit/phpunit ###
/phpunit.xml
.phpunit.result.cache
/.coverage-cache
/coverage-report-html/*
/coverage-report-xml/*
###< phpunit/phpunit ###