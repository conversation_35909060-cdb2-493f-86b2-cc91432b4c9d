{"name": "obrazcov.bg/tenants", "type": "project", "license": "MIT", "description": "A minimal Symfony project recommended to create bare bones applications", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.3.19", "ext-ctype": "*", "ext-iconv": "*", "doctrine/dbal": "^3.9.4", "doctrine/doctrine-bundle": "^2.14", "doctrine/doctrine-migrations-bundle": "^3.4.2", "doctrine/orm": "^3.3.3", "phpdocumentor/reflection-docblock": "^5.6.2", "phpstan/phpdoc-parser": "^2.1", "runtime/frankenphp-symfony": "^0.2.0", "stof/doctrine-extensions-bundle": "^1.14", "symfony/amqp-messenger": "7.2.*", "symfony/cache": "7.2.*", "symfony/console": "7.2.*", "symfony/doctrine-messenger": "7.2.*", "symfony/dotenv": "7.2.*", "symfony/expression-language": "7.2.*", "symfony/flex": "^2.7", "symfony/framework-bundle": "7.2.*", "symfony/http-client": "7.2.*", "symfony/intl": "7.2.*", "symfony/mailer": "7.2.*", "symfony/mime": "7.2.*", "symfony/monolog-bundle": "^3.10", "symfony/notifier": "7.2.*", "symfony/process": "7.2.*", "symfony/property-access": "7.2.*", "symfony/property-info": "7.2.*", "symfony/runtime": "7.2.*", "symfony/security-bundle": "7.2.*", "symfony/serializer": "7.2.*", "symfony/string": "7.2.*", "symfony/translation": "7.2.*", "symfony/uid": "7.2.*", "symfony/validator": "7.2.*", "symfony/yaml": "7.2.*", "zircote/swagger-php": "^5.1.3"}, "config": {"allow-plugins": {"php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true}, "bump-after-update": true, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/", "App\\Tests\\Shared\\Fixtures\\": "tests/Shared/Fixtures/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*", "symfony/polyfill-php82": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.2.*", "docker": true}}, "require-dev": {"coduo/php-matcher": "^6.0.17", "dama/doctrine-test-bundle": "^8.2.2", "dg/bypass-finals": "^1.9", "doctrine/doctrine-fixtures-bundle": "^4.1", "phpstan/phpstan": "^2.1.17", "phpunit/phpunit": "^9.6.23", "symfony/browser-kit": "7.2.*", "symfony/css-selector": "7.2.*", "symfony/debug-bundle": "7.2.*", "symfony/maker-bundle": "^1.63", "symfony/phpunit-bridge": "^7.2.6", "symfony/stopwatch": "7.2.*", "symfony/web-profiler-bundle": "7.2.*"}}