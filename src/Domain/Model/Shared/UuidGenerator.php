<?php

declare(strict_types=1);

namespace App\Domain\Model\Shared;

use Symfony\Component\Uid\Uuid;

final class UuidGenerator
{
    /**
     * Generate a new UUID v4 string
     */
    public static function generate(): string
    {
        return Uuid::v4()->toRfc4122();
    }

    /**
     * Check if a string is a valid UUID
     */
    public static function isValid(string $uuid): bool
    {
        return Uuid::isValid($uuid);
    }
}
