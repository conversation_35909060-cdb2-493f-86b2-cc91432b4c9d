<?php

declare(strict_types=1);

namespace App\Domain\Model\Organization;

use App\Domain\Model\Shared\UuidGenerator;
use DateTime;
use DateTimeInterface;

class Organization
{
    private ?string $id = null;
    private string $name;
    private string $email;
    private ?string $phone;
    private string $address;
    private ?DateTimeInterface $deletedAt = null;
    private ?DateTimeInterface $updatedAt = null;
    private ?DateTimeInterface $createdAt = null;

    public function __construct(
        string  $name,
        string  $email,
        ?string $phone,
        string  $address,
        ?string $id = null
    )
    {
        $this->id = $id ?? UuidGenerator::generate();
        $this->name = $name;
        $this->email = $email;
        $this->phone = $phone;
        $this->address = $address;
        $this->createdAt = new DateTime();
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
        $this->updatedAt = new DateTime();
    }

    public function getAddress(): string
    {
        return $this->address;
    }

    public function setAddress(string $address): void
    {
        $this->address = $address;
        $this->updatedAt = new DateTime();
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function setEmail(string $email): void
    {
        $this->email = $email;
        $this->updatedAt = new DateTime();
    }

    public function getPhone(): ?string
    {
        return $this->phone;
    }

    public function setPhone(?string $phone): void
    {
        $this->phone = $phone;
        $this->updatedAt = new DateTime();
    }

    public function getDeletedAt(): ?DateTimeInterface
    {
        return $this->deletedAt;
    }

    public function markAsDeleted(): void
    {
        $this->deletedAt = new DateTime();
    }

    public function restore(): void
    {
        $this->deletedAt = null;
    }

    public function getCreatedAt(): ?DateTimeInterface
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): ?DateTimeInterface
    {
        return $this->updatedAt;
    }

    public function update(
        string $name,
        string $email,
        ?string $phone,
        string $address
    ): void
    {
        $this->name = $name;
        $this->email = $email;
        $this->phone = $phone;
        $this->address = $address;
        $this->updatedAt = new DateTime();
    }
}

