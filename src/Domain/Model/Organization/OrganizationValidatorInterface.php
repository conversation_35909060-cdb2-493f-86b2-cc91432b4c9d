<?php

declare(strict_types=1);

namespace App\Domain\Model\Organization;

use App\Domain\Model\Error\ConstraintViolation;
use App\Domain\Model\Error\ConstraintViolationList;
use App\Domain\Model\Error\ValidatorInterface;

interface OrganizationValidatorInterface extends ValidatorInterface
{
    public function validateNameUniqueness(string $name, ?string $excludeId = null): ?ConstraintViolation;

    public function validateFilters(array $data): ConstraintViolationList;
}
