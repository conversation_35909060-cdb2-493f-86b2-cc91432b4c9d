<?php

declare(strict_types=1);

namespace App\Domain\Model\Organization;

use App\Domain\Model\ResultWithMetadata;

interface OrganizationRepositoryInterface
{
    public function save(Organization $organization): void;

    public function find(string $id): ?Organization;

    public function findByName(string $name): ?Organization;

    public function findByEmail(string $email): ?Organization;

    public function findByPhone(string $phone): ?Organization;

    public function findAll(): array;

    public function countAll(array $filters = []): int;

    public function remove(Organization $organization): void;

    public function search(int $getLimit, int $getOffset, array $filters, array $sort): ResultWithMetadata;
}
