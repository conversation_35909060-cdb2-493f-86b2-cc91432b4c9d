<?php

declare(strict_types=1);

namespace App\Domain\Model\Company;

use App\Domain\Model\Organization\Organization;
use App\Domain\Model\Shared\UuidGenerator;
use DateTime;
use DateTimeInterface;

class Company
{
    private ?string $id = null;
    private Organization $organization;
    private string $name;
    private string $email;
    private string $phone;
    private string $address;
    private string $defaultLanguage;
    private string $defaultCurrency;
    private ?DateTimeInterface $deletedAt = null;
    private ?DateTimeInterface $updatedAt = null;
    private ?DateTimeInterface $createdAt = null;

    public function __construct(
        Organization $organization,
        string       $name,
        string       $email,
        string       $phone,
        string       $address,
        string       $defaultLanguage,
        string       $defaultCurrency,
        ?string      $id = null
    )
    {
        $this->id = $id ?? UuidGenerator::generate();
        $this->organization = $organization;
        $this->name = $name;
        $this->email = $email;
        $this->phone = $phone;
        $this->address = $address;
        $this->defaultLanguage = $defaultLanguage;
        $this->defaultCurrency = $defaultCurrency;
        $this->createdAt = new DateTime();
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
        $this->updatedAt = new DateTime();
    }

    public function getAddress(): string
    {
        return $this->address;
    }

    public function setAddress(string $address): void
    {
        $this->address = $address;
        $this->updatedAt = new DateTime();
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function setEmail(string $email): void
    {
        $this->email = $email;
        $this->updatedAt = new DateTime();
    }

    public function getPhone(): string
    {
        return $this->phone;
    }

    public function setPhone(string $phone): void
    {
        $this->phone = $phone;
        $this->updatedAt = new DateTime();
    }

    public function getDefaultLanguage(): string
    {
        return $this->defaultLanguage;
    }

    public function setDefaultLanguage(string $defaultLanguage): void
    {
        $this->defaultLanguage = $defaultLanguage;
    }

    public function getDefaultCurrency(): string
    {
        return $this->defaultCurrency;
    }

    public function setDefaultCurrency(string $defaultCurrency): void
    {
        $this->defaultCurrency = $defaultCurrency;
    }

    public function getDeletedAt(): ?DateTimeInterface
    {
        return $this->deletedAt;
    }

    public function markAsDeleted(): void
    {
        $this->deletedAt = new DateTime();
    }

    public function restore(): void
    {
        $this->deletedAt = null;
    }

    public function getOrganization(): Organization
    {
        return $this->organization;
    }

    public function getCreatedAt(): ?DateTimeInterface
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): ?DateTimeInterface
    {
        return $this->updatedAt;
    }

    public function update(
        string $name,
        string $email,
        string $phone,
        string $address,
        string $defaultLanguage,
        string $defaultCurrency
    ): void
    {
        $this->name = $name;
        $this->email = $email;
        $this->phone = $phone;
        $this->address = $address;
        $this->defaultLanguage = $defaultLanguage;
        $this->defaultCurrency = $defaultCurrency;
        $this->updatedAt = new DateTime();
    }

    public function changeOrganization(Organization $organization): void
    {
        $this->organization = $organization;
        $this->updatedAt = new DateTime();
    }
}
