<?php

declare(strict_types=1);

namespace App\Domain\Model\Company;

interface CompanyRepositoryInterface
{
    public function save(Company $company): void;

    public function find(string $id): ?Company;

    public function findByName(string $name): ?Company;

    public function findByEmail(string $email): ?Company;

    public function findByPhone(string $phone): ?Company;

    public function findByWebsite(string $website): ?Company;

    public function findByOrganizationId(string $organizationId): array;

    public function findAll(): array;

    public function remove(Company $company): void;
}
