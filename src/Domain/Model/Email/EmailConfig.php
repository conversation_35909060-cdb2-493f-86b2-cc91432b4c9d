<?php

declare(strict_types=1);

namespace App\Domain\Model\Email;

final class EmailConfig
{
    public const TENANT_FROM_EMAIL = 'no-reply';

    public const FROM_NAME = 'Obrazcov';

    public const TEST_FROM_EMAIL = '<EMAIL>';

    /**
     * @param string|array{email: string, name?: string}              $to
     * @param ?array<int, string|array{email: string, name?: string}> $bcc
     * @param string|array{email: string, name?: string}              $from
     */
    public function __construct(
        private readonly string|array $to,
        private readonly ?array $bcc,
        private readonly string|array $from,
        private readonly string $subjectTemplate,
        private readonly array $subjectData,
        private readonly string $bodyTemplate,
        private readonly array $bodyData,
    ) {
    }

    /**
     * @return string|array{email: string, name?: string}
     */
    public function getTo(): string|array
    {
        return $this->to;
    }

    /**
     * @return ?array<int, string|array{email: string, name?: string}>
     */
    public function getBcc(): ?array
    {
        return $this->bcc;
    }

    /**
     * @return string|array{email: string, name?: string}
     */
    public function getFrom(): string|array
    {
        return $this->from;
    }

    public function getSubjectTemplate(): string
    {
        return $this->subjectTemplate;
    }

    /**
     * @return array<string, mixed>
     */
    public function getSubjectData(): array
    {
        return $this->subjectData;
    }

    public function getBodyTemplate(): string
    {
        return $this->bodyTemplate;
    }

    /**
     * @return array<string, mixed>
     */
    public function getBodyData(): array
    {
        return $this->bodyData;
    }
}
