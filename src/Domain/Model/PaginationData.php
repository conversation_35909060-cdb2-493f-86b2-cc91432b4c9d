<?php

declare(strict_types=1);

namespace App\Domain\Model;

final class PaginationData
{
    public function __construct(
        private readonly int $limit,
        private readonly int $offset,
        private readonly int $totalItemsCount
    )
    {
    }

    public function getLimit(): int
    {
        return $this->limit;
    }

    public function getOffset(): int
    {
        return $this->offset;
    }

    public function getTotalItemsCount(): int
    {
        return $this->totalItemsCount;
    }

    /**
     * @return array{limit: int, offset: int, total: int}
     */
    public function toArray(): array
    {
        return [
            'limit' => $this->getLimit(),
            'offset' => $this->getOffset(),
            'total' => $this->getTotalItemsCount(),
        ];
    }
}
