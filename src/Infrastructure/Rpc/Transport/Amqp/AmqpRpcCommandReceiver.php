<?php

declare(strict_types=1);

namespace App\Infrastructure\Rpc\Transport\Amqp;

use App\Infrastructure\Rpc\Exception\TimeoutException;
use App\Infrastructure\Rpc\RpcCommand;
use App\Infrastructure\Rpc\Server\RpcCommandServerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final class AmqpRpcCommandReceiver
{
    public function __construct(
        private readonly RpcCommandServerInterface $rpcServer,
    ) {
    }

    public function __invoke(RpcCommand $command): void
    {
        try {
            $this->rpcServer->handle($command);
        } catch (TimeoutException) {
            // discard the message
            return;
        }
    }
}
