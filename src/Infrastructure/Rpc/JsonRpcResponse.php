<?php

declare(strict_types=1);

namespace App\Infrastructure\Rpc;

use App\Infrastructure\Delivery\Api\V1\Documentation\Schema\JsonRpc\JsonRpcResponseSchema;
use OpenApi\Attributes as OA;

#[OA\Response(
    response: 'JsonRpcResponse',
    description: 'JSON RPC response',
    content: new OA\JsonContent(
        examples: [
            'error' => new OA\Examples(
                example: 'error',
                summary: 'Error',
                value: JsonRpcResponseSchema::EXAMPLE_ERROR,
            ),
            'getSingleTest' => new OA\Examples(
                example: 'getSingleTest',
                summary: 'Get single test',
                value: JsonRpcResponseSchema::EXAMPLE_GET_SINGLE_TEST,
            ),
        ],
        ref: '#/components/schemas/JsonRpcResponseSchema',
    ),
)]
abstract class JsonRpcResponse
{
    protected function __construct(
        protected readonly string $id,
        protected readonly string $jsonrpc = '2.0',
    ) {
    }

    public function getJsonrpc(): string
    {
        return $this->jsonrpc;
    }

    public function getId(): string
    {
        return $this->id;
    }
}
