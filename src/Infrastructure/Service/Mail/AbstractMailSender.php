<?php

declare(strict_types=1);

namespace App\Infrastructure\Service\Mail;

use App\Domain\Model\Email\EmailConfig;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Address;
use Symfony\Component\Mime\Email;
use Twig\Environment;

abstract class AbstractMailSender
{
    public function __construct(private readonly MailerInterface $mailer, private readonly Environment $twigRenderer)
    {
    }

    protected function sendMail(EmailConfig $config, bool $useFileTemplates = false): void
    {
        if ($useFileTemplates) {
            $subject = $this->twigRenderer->render($config->getSubjectTemplate(), $config->getSubjectData());
            $body = $this->twigRenderer->render($config->getBodyTemplate(), $config->getBodyData());
        } else {
            $subject = $this->twigRenderer->createTemplate($config->getSubjectTemplate())
                ->render($config->getSubjectData());
            $body = $this->twigRenderer->createTemplate($config->getBodyTemplate())
                ->render($config->getBodyData());
        }

        $email = (new Email())
            ->subject($subject)
            ->html($body)
            ->from($this->formatEmail($config->getFrom()))
            ->to($this->formatEmail($config->getTo()));

        $bcc = $config->getBcc();
        if (null !== $bcc && [] !== $bcc) {
            $email->bcc(...$this->formatEmails($bcc));
        }

        $this->mailer->send($email);
    }

    /**
     * @param array<int, string|array{email: string, name?: string}> $emails
     *
     * @return Address[]
     */
    private function formatEmails(array $emails): array
    {
        return array_map(fn (string|array $email) => $this->formatEmail($email), $emails);
    }

    /**
     * @param string|array{email: string, name?: string} $email
     */
    private function formatEmail(string|array $email): Address
    {
        return is_string($email) ? new Address($email) : new Address($email['email'], $email['name'] ?? '');
    }
}
