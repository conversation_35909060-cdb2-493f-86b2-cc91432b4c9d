<?php

declare(strict_types=1);

namespace App\Infrastructure\Service\Mail;

use App\Application\Service\Mail\MailSenderInterface;
use App\Domain\Model\Email\EmailConfig;

final class MailSender extends AbstractMailSender implements MailSenderInterface
{
    public function send(EmailConfig $config, bool $useFileTemplates = false): void
    {
        parent::sendMail($config, $useFileTemplates);
    }
}
