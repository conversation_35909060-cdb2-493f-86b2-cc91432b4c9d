<?php

declare(strict_types=1);

namespace App\Infrastructure\Service;

use App\Application\Service\SortServiceInterface;

final class SortService implements SortServiceInterface
{
    /**
     * @param string[] $allowedSortAttributes
     *
     * @return array{attribute: string, direction: string}[]
     */
    public function formatSort(array $allowedSortAttributes, string $sort): array
    {
        if (empty($sort)) {
            return [];
        }

        $formatted = [];
        $sortAttributes = explode(',', $sort);

        foreach ($sortAttributes as $sortAttribute) {
            [$column, $direction] = array_map('trim', explode('-', $sortAttribute));

            $direction = $direction ? strtolower($direction) : 'desc';

            if ($this->isValidSortAttribute($allowedSortAttributes, $column, $direction)) {
                $formatted[] = [
                    'attribute' => $column,
                    'direction' => $direction,
                ];
            }
        }

        return $formatted;
    }

    /**
     * @param string[] $allowedSortAttributes
     */
    private function isValidSortAttribute(array $allowedSortAttributes, string $column, string $direction = 'asc'): bool
    {
        return in_array($column, $allowedSortAttributes, true)
            && in_array($direction, ['asc', 'desc'], true);
    }
}
