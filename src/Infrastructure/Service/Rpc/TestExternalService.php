<?php
declare(strict_types=1);

namespace App\Infrastructure\Service\Rpc;

use App\Application\Query\Test\Get\GetTestQuery;
use App\Application\Query\Test\Get\GetTestQueryResponse;
use App\Application\Service\Rpc\TestExternalServiceInterface;
use App\Infrastructure\Rpc\Client\RpcCommandClientInterface;
use Exception;
use Psr\Log\LoggerInterface;
use Symfony\Component\Serializer\Exception\ExceptionInterface;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;

final class TestExternalService implements TestExternalServiceInterface
{
    private const SERVICE = 'tenants'; // replace with external service
    private const COMMAND_GET_SINGLE_TEST = 'getSingleTest';

    public function __construct(
        private readonly RpcCommandClientInterface $commandClient,
        private readonly LoggerInterface $logger,
        /** @phpstan-ignore property.onlyWritten */
        private readonly DenormalizerInterface $denormalizer,
    ) {
    }

    public function getTest(string $testId): ?GetTestQueryResponse
    {
        try {
            $response = $this->commandClient->call(
                service: self::SERVICE,
                command: self::COMMAND_GET_SINGLE_TEST,
                arguments: [new GetTestQuery($testId)],
                timeout: 30
            );

            if( null === $response->getResult()) {
                return null;
            }

            return GetTestQueryResponse::fromArray($response->getResult());
        } catch (Exception|ExceptionInterface $e) {
            $this->logger->warning($e->getMessage(), $e->getTrace());
        }

        return null;
    }
}
