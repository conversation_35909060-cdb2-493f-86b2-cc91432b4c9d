<?php

namespace App\Infrastructure\Domain\Model\Test;

use App\Domain\Model\Test\Test;
use App\Domain\Model\Test\TestRepositoryInterface;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;

class DoctrineTestRepository implements TestRepositoryInterface
{
    private EntityManagerInterface $entityManager;
    private EntityRepository $repository;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
        $this->repository = $entityManager->getRepository(Test::class);
    }

    public function findOneByValue(string $value): ?Test
    {
        return $this->repository->findOneBy(['value' => $value]);
    }

    public function findOneById(int $id): ?Test
    {
        return $this->repository->find($id);
    }

    /** @return Test[] */
    public function findAll(): array
    {
        return $this->repository->findAll();
    }

    public function save(Test $test): void
    {
        $this->entityManager->persist($test);
        $this->entityManager->flush();
    }
}
