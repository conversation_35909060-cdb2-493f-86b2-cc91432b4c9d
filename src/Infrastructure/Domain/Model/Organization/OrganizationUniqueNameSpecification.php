<?php

declare(strict_types=1);

namespace App\Infrastructure\Domain\Model\Organization;

use App\Domain\Model\Organization\OrganizationRepositoryInterface;
use App\Domain\Model\Organization\OrganizationUniqueNameSpecificationInterface;
use Doctrine\ORM\EntityManagerInterface;

class OrganizationUniqueNameSpecification implements OrganizationUniqueNameSpecificationInterface
{
    public function __construct(
        private readonly EntityManagerInterface          $em,
        private readonly OrganizationRepositoryInterface $organizationRepository
    )
    {
    }

    public function isSatisfiedBy(string $name, ?string $excludeId = null): bool
    {
        $this->em->getFilters()->disable('softdeleteable');

        try {
            $existingOrganization = $this->organizationRepository->findByName($name);

            if (null === $existingOrganization) {
                return true;
            }

            if (null !== $excludeId && $existingOrganization->getId() === $excludeId) {
                return true; 
            }

            return false;
        } finally {
            $this->em->getFilters()->enable('softdeleteable');
        }
    }
}
