<?php

declare(strict_types=1);

namespace App\Infrastructure\Domain\Model\Company;

use App\Domain\Model\Company\Company;
use App\Domain\Model\Company\CompanyRepositoryInterface;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

class DoctrineCompanyRepository extends ServiceEntityRepository implements CompanyRepositoryInterface
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Company::class);
    }

    public function save(Company $company): void
    {
        $this->getEntityManager()->persist($company);
        $this->getEntityManager()->flush();
    }

    public function find($id, $lockMode = null, ?int $lockVersion = null): ?Company
    {
        return $this->findOneBy(['id' => $id]);
    }

    public function findByName(string $name): ?Company
    {
        return $this->findOneBy(['name' => $name]);
    }

    public function findByEmail(string $email): ?Company
    {
        return $this->findOneBy(['email' => $email]);
    }

    public function findByPhone(string $phone): ?Company
    {
        return $this->findOneBy(['phone' => $phone]);
    }

    public function findByWebsite(string $website): ?Company
    {
        return $this->findOneBy(['website' => $website]);
    }

    /**
     * @return Company[]
     */
    public function findByOrganizationId(string $organizationId): array
    {
        return $this->createQueryBuilder('p')
            ->where('p.organization = :organizationId')
            ->setParameter('organizationId', $organizationId)
            ->getQuery()
            ->getResult();
    }

    /**
     * @return Company[]
     */
    public function findAll(): array
    {
        return $this->findBy([]);
    }

    public function remove(Company $company): void
    {
        $this->getEntityManager()->remove($company);
        $this->getEntityManager()->flush();
    }
}
