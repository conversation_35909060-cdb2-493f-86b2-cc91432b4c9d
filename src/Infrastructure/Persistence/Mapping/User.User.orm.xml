<?xml version="1.0" encoding="UTF-8"?>
<doctrine-mapping xmlns="http://doctrine-project.org/schemas/orm/doctrine-mapping"
                  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                  xmlns:gedmo="http://gediminasm.org/schemas/orm/doctrine-extensions-mapping"
                  xsi:schemaLocation="http://doctrine-project.org/schemas/orm/doctrine-mapping https://www.doctrine-project.org/schemas/orm/doctrine-mapping.xsd">
    <entity name="App\Domain\Model\User\User" table="users">
        <id name="id" type="integer" column="id">
            <generator strategy="AUTO"/>
        </id>
        <field name="email" length="180" unique="true"/>
        <field name="password"/>
        <field name="isVerified" type="boolean" column="is_verified"/>
        <field name="createdAt" type="datetime_immutable">
            <gedmo:timestampable on="create" />
        </field>
        <field name="updatedAt" type="datetime_immutable">
            <gedmo:timestampable on="update" />
        </field>
    </entity>
</doctrine-mapping>
