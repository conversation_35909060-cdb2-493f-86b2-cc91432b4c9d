<?xml version="1.0" encoding="utf-8"?>
<doctrine-mapping xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                  xmlns:gedmo="http://gediminasm.org/schemas/orm/doctrine-extensions-mapping"
                  xmlns="http://doctrine-project.org/schemas/orm/doctrine-mapping"
                  xsi:schemaLocation="http://doctrine-project.org/schemas/orm/doctrine-mapping https://www.doctrine-project.org/schemas/orm/doctrine-mapping.xsd">
    <entity name="App\Domain\Model\Company\Company" table="companies">
        <id name="id" type="guid" column="id">
            <options>
                <option name="fixed">true</option>
            </options>
        </id>

        <many-to-one field="organization" target-entity="App\Domain\Model\Organization\Organization">
            <join-column name="organization_id" referenced-column-name="id" nullable="false" on-delete="CASCADE">
                <options>
                    <option name="fixed">true</option>
                </options>
            </join-column>
        </many-to-one>

        <field name="name" type="string" length="100" nullable="false"/>
        <field name="email" type="string" length="255" nullable="false"/>
        <field name="phone" type="string" length="20" nullable="false"/>
        <field name="address" type="text" nullable="false"/>
        <field name="defaultLanguage" type="string" column="default_language" length="5" nullable="false"/>
        <field name="defaultCurrency" type="string" column="default_currency" length="3" nullable="false"/>

        <field name="deletedAt" type="datetime" column="deleted_at" nullable="true"/>
        <gedmo:soft-deleteable field-name="deletedAt" time-aware="false" hard-delete="true"/>

        <field name="updatedAt" type="datetime" column="updated_at" nullable="true">
            <gedmo:timestampable on="update"/>
        </field>
        <field name="createdAt" type="datetime" column="created_at" nullable="true">
            <gedmo:timestampable on="create"/>
        </field>

        <index name="idx_property_company_organization">
            <column name="organization_id"/>
        </index>
        <index name="idx_property_company_email">
            <column name="email"/>
        </index>
        <index name="idx_property_company_phone">
            <column name="phone"/>
        </index>
        <index name="idx_property_company_deleted_at">
            <column name="deleted_at"/>
        </index>

        <unique-constraints>
            <unique-constraint columns="name" name="uniq_property_company_name"/>
            <unique-constraint columns="email" name="uniq_property_company_email"/>
            <unique-constraint columns="phone" name="uniq_property_company_phone"/>
        </unique-constraints>

        <indexes>
            <index name="idx_property_company_organization" columns="organization_id"/>
        </indexes>
    </entity>
</doctrine-mapping>
