<?php

declare(strict_types=1);

namespace App\Infrastructure\Delivery\Api\V1\Organization;

use OpenApi\Attributes as OA;
use App\Application\Query\Organization\GetAll\GetAllOrganizationsQuery;
use App\Domain\Model\Bus\Query\QueryBusInterface;
use App\Infrastructure\Http\RequestMapper;
use App\Infrastructure\Http\ResponseMapper;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;

#[OA\Get(
    path: '/tenants/v1/{_locale}/organizations',
    summary: 'List Organizations',
    security: [['bearerAuth' => []]],
    tags: ['Organizations'],
    parameters: [
        new OA\Parameter(ref: '#/components/parameters/_locale'),
        new OA\Parameter(
            name: 'page',
            description: 'Page number',
            in: 'query',
            required: false,
            schema: new OA\Schema(type: 'integer', default: 1, minimum: 1)
        ),
        new OA\Parameter(
            name: 'limit',
            description: 'Items per page',
            in: 'query',
            required: false,
            schema: new OA\Schema(type: 'integer', default: 20, minimum: 1, maximum: 100)
        ),
        new OA\Parameter(
            name: 'sort',
            description: 'Sort criteria (format: attribute:direction, e.g., "name:asc" or "createdAt:desc")',
            in: 'query',
            required: false,
            schema: new OA\Schema(
                type: 'string',
                default: 'createdAt-desc',
                enum: ['name-asc', 'name-desc', 'createdAt-asc', 'createdAt-desc']
            )
        ),
        new OA\Parameter(
            name: 'filters[name]',
            description: 'Filter by organization name (partial match)',
            in: 'query',
            required: false,
            schema: new OA\Schema(type: 'string')
        ),
        new OA\Parameter(
            name: 'filters[email]',
            description: 'Filter by organization email (partial match)',
            in: 'query',
            required: false,
            schema: new OA\Schema(type: 'string')
        ),
    ],
    responses: [
        new OA\Response(
            response: Response::HTTP_OK,
            description: 'Organizations list',
            content: new OA\JsonContent(
                properties: [
                    new OA\Property(
                        property: 'data',
                        required: ['items', 'metadata'],
                        properties: [
                            new OA\Property(
                                property: 'items',
                                type: 'array',
                                items: new OA\Items(ref: '#/components/schemas/OrganizationResponse')
                            ),
                            new OA\Property(property: 'metadata', ref: '#/components/schemas/MetadataResponseSchema'),
                        ]
                    )
                ]
            )
        ),
        new OA\Response(ref: '#/components/responses/InternalServerErrorResponse', response: Response::HTTP_INTERNAL_SERVER_ERROR),
    ]
)]
final class GetOrganizationsAction extends AbstractController
{
    public function __construct(
        private readonly QueryBusInterface $queryBus,
        private readonly RequestMapper     $requestMapper,
        private readonly ResponseMapper    $responseMapper
    )
    {
    }

    /**
     * @throws \ReflectionException
     */
    #[Route('/organizations', name: 'app_get_organizations', methods: ['GET'])]
    public function __invoke(Request $request): JsonResponse
    {
        $query = $this->requestMapper->fromRequest($request, GetAllOrganizationsQuery::class);

        return $this->responseMapper->serializeResponse($this->queryBus->ask($query), Response::HTTP_OK);
    }
}
