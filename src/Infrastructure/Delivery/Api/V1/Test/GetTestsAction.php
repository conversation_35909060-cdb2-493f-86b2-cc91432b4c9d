<?php

declare(strict_types=1);

namespace App\Infrastructure\Delivery\Api\V1\Test;

use OpenApi\Attributes as OA;
use App\Application\Query\Test\GetAll\GetAllTestsQuery;
use App\Application\Query\Test\GetAll\GetAllTestsQueryResponse;
use App\Domain\Model\Bus\Query\QueryBusInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[OA\Get(
    path: '/tenants/v1/{_locale}/tests',
    summary: 'Get Tests',
    security: [['bearerAuth' => []]],
    tags: ['Tests'],
    parameters: [
        new OA\Parameter(ref: '#/components/parameters/_locale'),
    ],
    responses: [
        new OA\Response(ref: '#/components/responses/GetAllTestsQueryResponse', response: Response::HTTP_OK),
    ]
)]
class GetTestsAction extends AbstractController
{
    public function __construct(
        private readonly QueryBusInterface $query,
    ) {}
    #[Route('/tests', name: 'app_tests')]
    public function index(): Response
    {

        /** @var GetAllTestsQueryResponse $response */
        $response = $this->query->ask(new GetAllTestsQuery());

        return $this->json($response);
    }
}
