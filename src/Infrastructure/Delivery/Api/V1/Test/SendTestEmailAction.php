<?php

declare(strict_types=1);

namespace App\Infrastructure\Delivery\Api\V1\Test;

use OpenApi\Attributes as OA;
use App\Application\Command\Test\SendMail\SendTestMailCommand;
use App\Domain\Model\Bus\Command\CommandBusInterface;
use App\Infrastructure\Http\RequestMapper;
use App\Infrastructure\Http\ResponseMapper;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;
#[OA\Post(
    path: '/tenants/v1/{_locale}/test-email',
    summary: 'Send Test Email',
    security: [['bearerAuth' => []]],
    tags: ['Tests'],
    parameters: [
        new OA\Parameter(ref: '#/components/parameters/_locale'),
    ],
    responses: [
        new OA\Response(ref: '#/components/responses/GetAllTestsQueryResponse', response: Response::HTTP_OK),
    ]
)]
class SendTestEmailAction extends AbstractController
{
    public function __construct(
        private readonly CommandBusInterface $command,
        private readonly RequestMapper $requestMapper,
        private readonly ResponseMapper $responseMapper
    ) {}
    #[Route('/test-email', name: 'app_send_test_email', methods: ['POST'])]
    public function __invoke(Request $request): JsonResponse
    {
        /** @var SendTestMailCommand $command */
        $command = $this->requestMapper->fromRequest($request, SendTestMailCommand::class);
        $this->command->dispatch($command);;

        return $this->responseMapper->serializeResponse([], Response::HTTP_NO_CONTENT);
    }
}
