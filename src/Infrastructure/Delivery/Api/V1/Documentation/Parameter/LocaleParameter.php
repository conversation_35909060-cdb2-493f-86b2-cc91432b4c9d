<?php

declare(strict_types=1);

namespace App\Infrastructure\Delivery\Api\V1\Documentation\Parameter;

use OpenApi\Attributes as OA;

#[OA\Parameter(
    parameter: '_locale',
    name: '_locale',
    description: 'Locale',
    in: 'path',
    required: true,
    schema: new OA\Schema(
        type: 'string',
        default: 'en',
        enum: [
            'en',
            'bg',
        ]
    )
)]
final class LocaleParameter
{
}
