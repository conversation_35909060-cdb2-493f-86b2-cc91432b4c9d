<?php

declare(strict_types=1);

namespace App\Infrastructure\Delivery\Api\V1\Documentation\Response\Test;

use OpenApi\Attributes as OA;

#[OA\Response(
    response: 'GetAllTestsResponseSchema',
    description: 'Successful response',
    content: new OA\JsonContent(
        examples: [
            'default' => new OA\Examples(
                example: 'default',
                summary: 'Default',
                value: ['tests' => []],
            ),
        ],
        required: ['tests'],
        properties: [new OA\Property(property: 'tests', ref: '#/components/schemas/TestResponseSchema')]
    ),
)]
final readonly class GetAllTestsResponseSchema {}
