<?php

declare(strict_types=1);

namespace App\Infrastructure\Delivery\Api\V1\Documentation\Response\Organization;

use App\Infrastructure\Delivery\Api\V1\Documentation\Schema\Organization\OrganizationResponseSchema;
use OpenApi\Attributes as OA;

#[OA\Response(
    response: 'SingleOrganizationResponse',
    description: 'Successful company response',
    content: new OA\JsonContent(
        examples: [
            'default' => new OA\Examples(
                example: 'default',
                summary: 'Default',
                value: ['data' => OrganizationResponseSchema::EXAMPLE_DEFAULT],
            ),
        ],
        required: ['data'],
        properties: [new OA\Property(property: 'data', ref: '#/components/schemas/OrganizationResponse')],
    ),
)]
final class SingleOrganizationResponse
{
}
