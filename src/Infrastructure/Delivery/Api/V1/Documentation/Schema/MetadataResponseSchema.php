<?php

declare(strict_types=1);

namespace App\Infrastructure\Delivery\Api\V1\Documentation\Schema;

use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'MetadataResponseSchema',
    required: ['limit', 'offset', 'total'],
    properties: [
        new OA\Property(property: 'limit', type: 'integer', maximum: 100, minimum: 1),
        new OA\Property(property: 'offset', type: 'integer', minimum: 0),
        new OA\Property(property: 'total', type: 'integer', minimum: 0),
    ]
)]
final class MetadataResponseSchema
{
    public const EXAMPLE_DEFAULT = [
        'limit' => 25,
        'offset' => 0,
        'total' => 1,
    ];
}
