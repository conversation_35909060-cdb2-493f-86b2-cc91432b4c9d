<?php

declare(strict_types=1);

namespace App\Infrastructure\Delivery\Api\V1\Documentation\Schema\Organization;

use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'OrganizationResponse',
    required: ['id', 'name', 'email', 'address', 'deletedAt', 'createdAt', 'updatedAt'],
    properties: [
        new OA\Property(property: 'id', type: 'string', format: 'uuid'),
        new OA\Property(property: 'name', type: 'string'),
        new OA\Property(property: 'email', type: 'string', format: 'email'),
        new OA\Property(property: 'phone', type: 'string', nullable: true),
        new OA\Property(property: 'address', type: 'string'),
        new OA\Property(property: 'deletedAt', type: 'string', format: 'date-time', nullable: true),
        new OA\Property(property: 'createdAt', type: 'string', format: 'date-time'),
        new OA\Property(property: 'updatedAt', type: 'string', format: 'date-time'),
    ],
    type: 'object',
    example: [
        'id' => 'cff92407-d772-447f-af6b-d53722361948',
        'name' => 'ACME Holdings Ltd',
        'email' => '<EMAIL>',
        'phone' => '******-0123',
        'address' => '123 Business Ave, New York, NY 10001, US',
        'deletedAt' => null,
        'createdAt' => '2025-06-02T13:00:00Z',
        'updatedAt' => '2025-06-02T13:00:00Z',
    ]
)]
final class OrganizationResponseSchema
{
    public const array EXAMPLE_DEFAULT = [
        'id' => 'cff92407-d772-447f-af6b-d53722361948',
        'name' => 'ACME Holdings Ltd',
        'email' => '<EMAIL>',
        'phone' => '******-0123',
        'address' => '123 Business Ave, New York, NY 10001, US',
        'deletedAt' => null,
        'createdAt' => '2025-06-02T13:00:00Z',
        'updatedAt' => '2025-06-02T13:00:00Z',
    ];
}
