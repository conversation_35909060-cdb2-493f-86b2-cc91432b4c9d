<?php

declare(strict_types=1);

namespace App\Infrastructure\Delivery\Api\V1\Documentation\Schema\Test;

use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'TestResponseSchema',
    required: ['value'],
    properties: [
        new OA\Property(property: 'id', type: 'integer', nullable: true),
        new OA\Property(property: 'value', type: 'string'),
        new OA\Property(property: 'createdAt', type: 'string', format: 'date-time', nullable: true),
        new OA\Property(property: 'updatedAt', type: 'string', format: 'date-time', nullable: true),
    ],
    type: 'object'
)]
final class TestResponseSchema
{
    public const EXAMPLE = [
        'id' => 1,
        'value' => 'Example Value',
        'createdAt' => '2024-01-01T12:00:00+00:00',
        'updatedAt' => '2024-01-02T12:00:00+00:00',
    ];
}
