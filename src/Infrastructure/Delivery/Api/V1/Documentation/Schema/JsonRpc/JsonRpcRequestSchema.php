<?php

declare(strict_types=1);

namespace App\Infrastructure\Delivery\Api\V1\Documentation\Schema\JsonRpc;

use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'JsonRpcRequestSchema',
    required: ['jsonrpc', 'method'],
    properties: [
        new OA\Property(property: 'jsonrpc', type: 'string', default: '2.0'),
        new OA\Property(property: 'method', type: 'string'),
        new OA\Property(property: 'params', type: 'array', items: new OA\Items()),
        new OA\Property(property: 'id', type: 'string', format: 'uuid'),
    ],
)]
final class JsonRpcRequestSchema
{
    public const EXAMPLE_GET_SINGLE_TEST = [
        'jsonrpc' => '2.0',
        'method' => 'getSingleTest',
        'params' => [
            [
                'testId' => '1',
            ],
        ],
        'id' => 'cff92407-d772-447f-af6b-d53722361948',
    ];
}
