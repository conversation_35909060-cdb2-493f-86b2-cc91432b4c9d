<?php

declare(strict_types=1);

namespace App\Application\Shared\Organization;

use App\Domain\Model\Bus\Command\CommandResponseInterface;
use App\Domain\Model\Organization\Organization;
use App\Domain\Model\Bus\Query\QueryResponseInterface;
use DateTimeInterface;

final readonly class OrganizationResponse implements QueryResponseInterface, CommandResponseInterface
{
    public function __construct(
        private string             $id,
        private string             $name,
        private string             $email,
        private ?string            $phone,
        private string             $address,
        private ?DateTimeInterface $deletedAt,
        private ?DateTimeInterface $createdAt,
        private ?DateTimeInterface $updatedAt
    )
    {
    }

    public function getId(): string
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function getPhone(): ?string
    {
        return $this->phone;
    }

    public function getAddress(): string
    {
        return $this->address;
    }

    public function getDeletedAt(): ?string
    {
        return $this->deletedAt?->format('c');
    }

    public function getCreatedAt(): ?string
    {
        return $this->createdAt?->format('c');
    }

    public function getUpdatedAt(): ?string
    {
        return $this->updatedAt?->format('c');
    }

    public static function fromOrganization(Organization $organization): self
    {
        return new self(
            $organization->getId(),
            $organization->getName(),
            $organization->getEmail(),
            $organization->getPhone(),
            $organization->getAddress(),
            $organization->getDeletedAt(),
            $organization->getCreatedAt(),
            $organization->getUpdatedAt()
        );
    }
}
