<?php

declare(strict_types=1);

namespace App\Application\Command\Organization\Delete;

use App\Application\Shared\Error\ErrorResponse;
use App\Domain\Model\Organization\OrganizationRepositoryInterface;

final readonly class DeleteOrganizationCommandHandler
{
    public function __construct(
        private readonly OrganizationRepositoryInterface $organizationRepository
    )
    {
    }

    public function __invoke(DeleteOrganizationCommand $command): ?ErrorResponse
    {
        $organization = $this->organizationRepository->find($command->getId());

        if (null === $organization) {
            return ErrorResponse::notFound('Organization not found');
        }

        $organization->markAsDeleted();
        $this->organizationRepository->save($organization);

        return null;
    }
}
