<?php

declare(strict_types=1);

namespace App\Application\Command\Organization\Create;

use App\Application\Command\AbstractCommand;

final class CreateOrganizationCommand extends AbstractCommand
{
    public function __construct(
        private readonly string  $name,
        private readonly string  $email,
        private readonly ?string $phone,
        private readonly string  $address
    )
    {
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function getPhone(): ?string
    {
        return $this->phone;
    }

    public function getAddress(): string
    {
        return $this->address;
    }
}
