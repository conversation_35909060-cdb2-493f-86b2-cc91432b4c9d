<?php

declare(strict_types=1);

namespace App\Application\Command\Test\SendMail;

use App\Application\Service\Mail\MailSenderInterface;
use App\Domain\Model\Email\EmailConfig;

final readonly class SendTestMailCommandHandler
{

    public function __construct(
        private readonly MailSenderInterface $mailSender,
    ) {}

    public function __invoke(SendTestMailCommand $query): void
    {
        $emailConfig = new EmailConfig(
            to: '<EMAIL>',
            bcc: null,
            from: EmailConfig::TEST_FROM_EMAIL,
            subjectTemplate: 'Test Email Subject',
            subjectData: [],
            bodyTemplate: 'Test Email Body',
            bodyData: [],
        );

        $this->mailSender->send($emailConfig);
    }
}
