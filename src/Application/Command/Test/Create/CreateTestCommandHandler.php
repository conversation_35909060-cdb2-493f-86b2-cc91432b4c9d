<?php

declare(strict_types=1);

namespace App\Application\Command\Test\Create;

use App\Domain\Model\Test\Test;
use App\Domain\Model\Test\TestRepositoryInterface;

final readonly class CreateTestCommandHandler
{
    public function __construct(private readonly TestRepositoryInterface $testRepository)
    {
    }

    public function __invoke(CreateTestCommand $command): void
    {
        $test = new Test($command->getValue());
        $this->testRepository->save($test);
    }
}
