<?php

declare(strict_types=1);

namespace App\Application\EventHandler\Test;

use App\Domain\Model\Test\TestCreatedEvent;
use Psr\Log\LoggerInterface;

final readonly class TestCreatedEventHandler
{

    public function __construct(private LoggerInterface $logger)
    {
    }

    public function __invoke(TestCreatedEvent $event): void
    {
        $this->logger->warning("From Event: " .$event->getName());
    }
}
