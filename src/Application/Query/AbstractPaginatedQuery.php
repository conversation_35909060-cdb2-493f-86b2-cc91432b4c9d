<?php

declare(strict_types=1);

namespace App\Application\Query;

use App\Domain\Model\Bus\Query\QueryInterface;

abstract class AbstractPaginatedQuery implements PaginatedQueryInterface, QueryInterface
{
    public function __construct(private readonly int $limit, private readonly int $offset)
    {
    }

    public function getLimit(): int
    {
        if ($this->limit >= self::MAX_ITEMS_LIMIT) {
            return self::MAX_ITEMS_LIMIT;
        }

        if ($this->limit <= 0) {
            return self::DEFAULT_ITEMS_LIMIT;
        }

        return $this->limit;
    }

    public function getOffset(): int
    {
        return max($this->offset, 0);
    }
}
