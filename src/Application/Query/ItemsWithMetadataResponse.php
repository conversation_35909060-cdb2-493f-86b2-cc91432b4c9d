<?php

declare(strict_types=1);

namespace App\Application\Query;

use App\Domain\Model\Bus\Query\QueryResponseInterface;
use App\Domain\Model\ResultWithMetadata;

final class ItemsWithMetadataResponse implements QueryResponseInterface
{
    private const DEFAULT_DATA = [
        'items' => [],
        'metadata' => [
            'limit' => 0,
            'offset' => 0,
            'total' => 0,
        ],
    ];

    private readonly array $data;

    /**
     * @param array{items: array, metadata: array{limit: int, offset: int, total: int}} $data
     */
    private function __construct(array $data = self::DEFAULT_DATA)
    {
        $this->data = $data;
    }

    /**
     * @return array<string, array>
     */
    public function getData(): array
    {
        return $this->data;
    }

    /**
     * @param callable $callback - applied to all items in the result
     */
    public static function create(ResultWithMetadata $resultWithMetadata, callable $callback): self
    {
        return new self($resultWithMetadata->toArray($callback));
    }

    public static function createEmpty(): self
    {
        return new self();
    }
}
