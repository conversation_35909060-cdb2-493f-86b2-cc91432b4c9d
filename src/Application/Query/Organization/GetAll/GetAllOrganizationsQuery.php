<?php

declare(strict_types=1);

namespace App\Application\Query\Organization\GetAll;

use App\Application\Query\AbstractPaginatedQuery;
use App\Domain\Model\Bus\Query\QueryInterface;

final class GetAllOrganizationsQuery extends AbstractPaginatedQuery
{
    /**
     * @var array<string, array<string, string>>
     */
    private readonly array $filters;

    private readonly string $sort;

    public function __construct(int $limit, int $offset, array $filters, string $sort)
    {
        parent::__construct($limit, $offset);

        $this->filters = $filters;
        $this->sort = $sort;
    }

    /**
     * @return array<string, array<string, string>>
     */
    public function getFilters(): array
    {
        return $this->filters;
    }

    public function getSort(): string
    {
        return $this->sort;
    }
}
