<?php

declare(strict_types=1);

namespace App\Application\Query\Test\Get;

use OpenApi\Attributes as OA;
use App\Domain\Model\Bus\Query\QueryResponseInterface;
use DateTimeImmutable;
use Exception;

#[OA\Schema(
    schema: 'GetTestQueryResponse',
    type: 'object',
    required: ['id', 'value', 'createdAt'],
    properties: [
        new OA\Property(property: 'id', type: 'integer'),
        new OA\Property(property: 'value', type: 'string'),
        new OA\Property(property: 'createdAt', type: 'string', format: 'date-time'),
    ],
    example: [
        'id' => 1,
        'value' => 'test value',
        'createdAt' => '2025-04-18T14:59:56+05:00',
    ]
)]
final readonly class GetTestQueryResponse implements QueryResponseInterface
{
    public function __construct(
        private readonly int $id,
        private readonly string $value,
        private readonly DateTimeImmutable $createdAt,
    ) {}


    /**
     * @throws Exception
     */
    public static function fromArray(array $data): self
    {
        return new self(
            (int) $data['id'],
            (string) $data['value'],
            DateTimeImmutable::createFromFormat('Y-m-d H:i:s', (string) $data['createdAt']),
        );
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getValue(): string
    {
        return $this->value;

    }

    public function getCreatedAt(): string
    {
        return $this->createdAt->format('Y-m-d H:i:s');
    }
}
