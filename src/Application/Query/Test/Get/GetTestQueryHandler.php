<?php

declare(strict_types=1);

namespace App\Application\Query\Test\Get;

use App\Domain\Model\Test\Test;
use App\Domain\Model\Test\TestRepositoryInterface;
use DateTimeImmutable;

final readonly class GetTestQueryHandler
{
    public function __construct(
        private readonly TestRepositoryInterface $repository,
    ) {}

    public function __invoke(GetTestQuery $query): ?GetTestQueryResponse
    {
        $test = $this->repository->findOneById((int) $query->getTestId());
        if (null === $test) {
            return null;
        }

        return new GetTestQueryResponse($test->getId() ?? 0, $test->getValue(), $test->getCreatedAt());
    }
}
