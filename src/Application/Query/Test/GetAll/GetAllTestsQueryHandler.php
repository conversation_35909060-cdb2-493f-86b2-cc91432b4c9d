<?php

declare(strict_types=1);

namespace App\Application\Query\Test\GetAll;

use App\Domain\Model\Test\TestRepositoryInterface;
use App\Application\Command\Test\Create\CreateTestCommand;
use App\Application\Service\Rpc\TestExternalServiceInterface;
use App\Domain\Model\Bus\Command\CommandBusInterface;
use App\Domain\Model\Bus\Event\EventBusInterface;
use App\Domain\Model\Test\TestCreatedEvent;

final readonly class GetAllTestsQueryHandler
{

    public function __construct(
        private readonly TestRepositoryInterface $repository,
        private readonly CommandBusInterface $command,
        private readonly EventBusInterface $eventBus,
        private readonly TestExternalServiceInterface $testExternalService
    ){}

    public function __invoke(GetAllTestsQuery $query): GetAllTestsQueryResponse
    {
        $tests = $this->repository->findAll();

        $this->command->dispatch(new CreateTestCommand('Hello World Command'));

        $this->eventBus->publish(new TestCreatedEvent('Hello World Command'));

        // using RPC
        $test = $this->testExternalService->getTest( (string) (count($tests) + 1));

        return new GetAllTestsQueryResponse(tests: $tests, rpcTest: $test);
    }
}
