<?php

declare(strict_types=1);

namespace App\Application\Query\Test\GetAll;

use App\Application\Query\Test\Get\GetTestQueryResponse;
use OpenApi\Attributes as OA;
use App\Domain\Model\Bus\Query\QueryResponseInterface;

#[OA\Response(
    response: 'GetAllTestsQueryResponse',
    description: 'Successful response',
    content: new OA\JsonContent(
        examples: [
            'default' => new OA\Examples(
                example: 'default',
                summary: 'Default',
                value: ['tests' => [], 'rpcTest' => null],
            ),
        ],
        required: ['tests', 'rpcTest'],
        properties: [
            new OA\Property(
                property: 'tests',
                type: 'array',
                items: new OA\Items(ref: '#/components/schemas/GetTestQueryResponse')
            ),
            new OA\Property(
                property: 'rpcTest',
                ref: '#/components/schemas/GetTestQueryResponse'
            )
        ]
    ),
)]
final readonly class GetAllTestsQueryResponse implements QueryResponseInterface
{
    public function __construct(
        private readonly array $tests,
        private readonly ?GetTestQueryResponse $rpcTest
    ) {
    }

    public function getTests(): array
    {
        return $this->tests;
    }

    public function getRpcTest(): ?GetTestQueryResponse
    {
        return $this->rpcTest;
    }
}
