DOCKER_COMPOSE_ENV = $(shell cat ../infrastructure/.env | grep DOCKER_COMPOSE_ENV | sed -E 's/.*=(.*)/\1/')
CONTAINER = $(DOCKER_COMPOSE_ENV)-tenants
# Executables (local)
DOCKER_COMP = docker compose

# Docker containers
PHP_CONT = $(DOCKER_COMP) exec php

# Executables
PHP      = $(PHP_CONT) php
COMPOSER = $(PHP_CONT) composer
SYMFONY  = $(PHP) bin/console

# Misc
.DEFAULT_GOAL = help
.PHONY        : help build up start down logs sh composer vendor sf cc test

## —— 🎵 🐳 The Symfony Docker Makefile 🐳 🎵 ——————————————————————————————————
help: ## Outputs this help screen
	@grep -E '(^[a-zA-Z0-9\./_-]+:.*?##.*$$)|(^##)' $(MAKEFILE_LIST) | awk 'BEGIN {FS = ":.*?## "}{printf "\033[32m%-30s\033[0m %s\n", $$1, $$2}' | sed -e 's/\[32m##/[33m/'

## —— Docker 🐳 ————————————————————————————————————————————————————————————————
build: ## Builds the Docker images
	@$(DOCKER_COMP) build --pull --no-cache

up: ## Start the docker hub in detached mode (no logs)
	@$(DOCKER_COMP) up --detach

start: build up ## Build and start the containers

down: ## Stop the docker hub
	@$(DOCKER_COMP) down --remove-orphans

logs: ## Show live logs
	@$(DOCKER_COMP) logs --tail=0 --follow

sh: ## Connect to t container
	@$(PHP_CONT) sh

bash: ## Connect to the FrankenPHP container via bash so up and down arrows go to previous commands
	@$(PHP_CONT) bash

#test: ## Start tests with phpunit, pass the parameter "c=" to add options to phpunit, example: make test c="--group e2e --stop-on-failure"
#	@$(eval c ?=)
#	@$(DOCKER_COMP) exec -e APP_ENV=test php bin/phpunit $(c)


## —— Composer 🧙 ——————————————————————————————————————————————————————————————
composer: ## Run composer, pass the parameter "c=" to run a given command, example: make composer c='req symfony/orm-pack'
	@$(eval c ?=)
	@$(COMPOSER) $(c)

vendor: ## Install vendors according to the current composer.lock file
vendor: c=install --prefer-dist --no-dev --no-progress --no-scripts --no-interaction
vendor: composer

## —— Symfony 🎵 ———————————————————————————————————————————————————————————————
sf: ## List all Symfony commands or pass the parameter "c=" to run a given command, example: make sf c=about
	@$(eval c ?=)
	@$(SYMFONY) $(c)

cc: c=c:c ## Clear the cache
cc: sf

## —— Doctrine 📚 ———————————————————————————————————————————————————————————————
doctrine-migrate: ## Run Doctrine migrations
	@$(SYMFONY) doctrine:migrations:migrate --no-interaction

doctrine-diff: ## Generate a new migration from entity changes
	docker exec -it $(CONTAINER) sh -c "php bin/console doctrine:migrations:diff"

doctrine-validate: ## Validate Doctrine mapping
	@$(SYMFONY) doctrine:schema:validate

doctrine-prev: ## Revert the last migration
	@$(SYMFONY) doctrine:migrations:migrate prev --no-interaction

doctrine-down: ## Execute the down() method of a specific migration: make doctrine-exec-down v=Version20250406190640
	@$(eval v ?=)
	@if [ -z "$(v)" ]; then echo "❌ Please provide a version: make doctrine-exec-down v=VersionYYYYMMDDHHMMSS"; exit 1; fi
	@$(SYMFONY) doctrine:migrations:execute --down DoctrineMigrations\\$(v) --no-interaction

terminal:
	docker exec -it $(CONTAINER) bash

clear-cache: ## Clear and warm up cache
	docker exec -it $(CONTAINER) sh -c "php bin/console cache:clear"

test: ## Run all tests
	docker exec -it $(CONTAINER) sh -c "php vendor/bin/phpunit $(if $(filter),--filter=$(filter)) $(if $(testsuite),--testsuite=$(testsuite))"

phpstan:
	docker exec -it $(CONTAINER) vendor/bin/phpstan analyse --memory-limit=4G

fixtures:
	docker exec -it $(CONTAINER) "php bin/console doctrine:fixtures:load"
