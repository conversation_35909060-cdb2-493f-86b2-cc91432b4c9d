<?php

declare(strict_types=1);

namespace DoctrineMigrations\migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250611113457 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'creates the organization and company tables';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            CREATE TABLE companies (id CHAR(36) NOT NULL COMMENT '(DC2Type:guid)', organization_id CHAR(36) NOT NULL COMMENT '(DC2Type:guid)', name VARCHAR(100) NOT NULL, email VARCHAR(255) NOT NULL, phone VARCHAR(20) NOT NULL, address LONGTEXT NOT NULL, default_language VARCHAR(5) NOT NULL, default_currency VARCHAR(3) NOT NULL, deleted_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, created_at DATETIME DEFAULT NULL, INDEX idx_property_company_organization (organization_id), UNIQUE INDEX uniq_property_company_name (name), UNIQUE INDEX uniq_property_company_email (email), UNIQUE INDEX uniq_property_company_phone (phone), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL
        );
        $this->addSql(<<<'SQL'
            CREATE TABLE organizations (id CHAR(36) NOT NULL COMMENT '(DC2Type:guid)', name VARCHAR(100) NOT NULL, email VARCHAR(255) NOT NULL, phone VARCHAR(20) NOT NULL, address LONGTEXT NOT NULL, deleted_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, created_at DATETIME DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL
        );
        $this->addSql(<<<'SQL'
            ALTER TABLE companies ADD CONSTRAINT FK_8244AA3A32C8A3DE FOREIGN KEY (organization_id) REFERENCES organizations (id) ON DELETE CASCADE
        SQL
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            ALTER TABLE companies DROP FOREIGN KEY FK_8244AA3A32C8A3DE
        SQL
        );
        $this->addSql(<<<'SQL'
            DROP TABLE companies
        SQL
        );
        $this->addSql(<<<'SQL'
            DROP TABLE organizations
        SQL
        );
    }
}
