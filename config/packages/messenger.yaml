framework:
    messenger:
        failure_transport: null
        
        default_bus: event.bus
            
        buses:
            command.bus:
            query.bus:
            event.bus:
                middleware:
                    - App\Infrastructure\Messenger\LogMessageMiddleware

        serializer:
            default_serializer: App\Infrastructure\Messenger\MessageSerializer

        transports:
            # https://symfony.com/doc/current/messenger.html#transport-configuration
            async:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
#                options:
#                    use_notify: true
#                    check_delayed_interval: 60000
                retry_strategy:
                    max_retries: 3
                    multiplier: 2
                    
            async_test_created:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                retry_strategy:
                    max_retries: 3
                    multiplier: 2
                options:
                    exchange:
                        name: async_test_created
                        type: direct
                    queues:
                        async_test_created: ~
            sync_rpc:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                serializer: messenger.transport.json_serializer
                retry_strategy:
                    delay: 2000
                    max_retries: 5
                    multiplier: 2
                options:
                    exchange:
                        name: rpc
                        type: topic
                    queues:
                        '%app.service_name%.sync_rpc':
                            binding_keys: ['%app.service_name%.#']
            async_rpc:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                serializer: messenger.transport.json_serializer
                retry_strategy:
                    delay: 2000
                    max_retries: 5
                    multiplier: 2
                options:
                    exchange:
                        name: rpc
                        type: topic
                    queues:
                        '%app.service_name%.async_rpc':
                            binding_keys: ['%app.service_name%.#']

        routing:
            'App\Infrastructure\Rpc\RpcMessage': async_rpc
            'App\Infrastructure\Rpc\RpcCommand': sync_rpc
            'App\Domain\Model\Test\TestCreatedEvent': async_test_created
