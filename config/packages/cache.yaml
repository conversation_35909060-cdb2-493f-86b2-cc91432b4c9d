framework:
    cache:
        # Unique name of your app: used to compute stable namespaces for cache keys.
        prefix_seed: obrazcov/obrazcov

        # The "app" cache stores to the filesystem by default.
        # The data in this cache should persist between deploys.
        # Other options include:

        # Redis
        app: cache.adapter.redis
        default_redis_provider: '%app.redis_dsn%'

        # APCu (not recommended with heavy random-write workloads as memory fragmentation can cause perf issues)
        #app: cache.adapter.apcu
