#!/bin/sh

DOCKER_COMPOSE_ENV=$(cat ../infrastructure/.env | grep DOCKER_COMPOSE_ENV | sed -E 's/.*=(.*)/\1/')
CONTAINER=$DOCKER_COMPOSE_ENV-tenants

echo "\n*** Run phpstan on all files ***"
docker exec -t $CONTAINER vendor/bin/phpstan analyse -c phpstan.dist.neon --memory-limit=4G
EXIT_CODE=$?
if [ "$EXIT_CODE" -gt 0 ]
then
  echo "\e[31m*** Fix phpstan errors ***\e[0m"
  exit $EXIT_CODE
fi
echo "*** <PERSON><PERSON><PERSON> finished without errors ***"

echo ""
echo "*** Running PHPUnit tests ***"
docker exec -t $CONTAINER vendor/bin/phpunit

if [ $? -ne 0 ]; then
    echo "❌ PHPUnit tests failed. Aborting commit."
    exit 1
fi
echo "*** PHPUnit tests passed ***"

echo ""
echo "*** Validating database schema ***"
docker exec -t $CONTAINER sh -c "php bin/console doctrine:schema:validate"
EXIT_CODE=$?
if [ "$EXIT_CODE" -gt 0 ]; then
    echo "\033[0;31m*** Sync main DB schema with mapping files ***\033[0m"
    exit $EXIT_CODE
fi
echo "*** Main database schema is valid ***"
